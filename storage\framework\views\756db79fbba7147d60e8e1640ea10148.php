<?php
    $tablePagination = $this->featureSettings->isNotEmpty() && $this->totalFeatureSettings > 5 ? true : false;
    $featureSettingsAvailable = $tablePagination;
    $rowNumber = 0; // Initialize row number
?>
<div>

    <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('backend.settings.system.feature-settings.create-feature-settings-component', []);

$__html = app('livewire')->mount($__name, $__params, 'lw-2803509099-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>

    
    <div>
        <?php if (isset($component)) { $__componentOriginal2848fab3424fc8162748b5c6984d5047 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2848fab3424fc8162748b5c6984d5047 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.filter','data' => ['filters' => [
            [
                'name' => 'filterFeatureSettingsByValue',
                'label' => 'Choose by value',
                'options' =>$this->featureSettingValueOptions,
            ],
        ],'results' => $featureSettingsAvailable]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filter'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['filters' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
            [
                'name' => 'filterFeatureSettingsByValue',
                'label' => 'Choose by value',
                'options' =>$this->featureSettingValueOptions,
            ],
        ]),'results' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($featureSettingsAvailable)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2848fab3424fc8162748b5c6984d5047)): ?>
<?php $attributes = $__attributesOriginal2848fab3424fc8162748b5c6984d5047; ?>
<?php unset($__attributesOriginal2848fab3424fc8162748b5c6984d5047); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2848fab3424fc8162748b5c6984d5047)): ?>
<?php $component = $__componentOriginal2848fab3424fc8162748b5c6984d5047; ?>
<?php unset($__componentOriginal2848fab3424fc8162748b5c6984d5047); ?>
<?php endif; ?>
    </div>

    
    <?php if (isset($component)) { $__componentOriginal9b33c063a2222f59546ad2a2a9a94bc6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9b33c063a2222f59546ad2a2a9a94bc6 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.search','data' => ['results' => $featureSettingsAvailable,'searchProperty' => 'featureSettingsSearch','searchPlaceholder' => 'Search by feature name....','formWidth' => 'max-w-full my-3']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('search'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['results' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($featureSettingsAvailable),'searchProperty' => 'featureSettingsSearch','searchPlaceholder' => 'Search by feature name....','formWidth' => 'max-w-full my-3']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9b33c063a2222f59546ad2a2a9a94bc6)): ?>
<?php $attributes = $__attributesOriginal9b33c063a2222f59546ad2a2a9a94bc6; ?>
<?php unset($__attributesOriginal9b33c063a2222f59546ad2a2a9a94bc6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9b33c063a2222f59546ad2a2a9a94bc6)): ?>
<?php $component = $__componentOriginal9b33c063a2222f59546ad2a2a9a94bc6; ?>
<?php unset($__componentOriginal9b33c063a2222f59546ad2a2a9a94bc6); ?>
<?php endif; ?>

    
    <?php if (isset($component)) { $__componentOriginal163c8ba6efb795223894d5ffef5034f5 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal163c8ba6efb795223894d5ffef5034f5 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.table','data' => ['columns' => [
        [
            'label' => '#',
            'headerClass' => 'border-slate-600 w-24',
        ],
        [
            'label' => 'Feature name',
            'columnName' => 'feature', // <-- for the arrow
            'headerClass' => 'border-l border-slate-600 w-48',
        ],
        [
            'label' => 'Value',
            'columnName' => 'value',
            'headerClass' => 'border-l border-slate-600 w-32',
        ],
        [
            'label' => 'Actions',
            'headerClass' => 'border-l border-slate-600 w-24',
        ],
    ],'data' => $this->featureSettings,'captionClass' => 'text-slate-200','sortDirection' => $sortDirection,'sortColumn' => $sortColumn,'results' => $featureSettingsAvailable,'maxScrollHeight' => 'max-h-[500px] hidden lg:block pr-1','theadClass' => 'bg-slate-700 border-l border-slate-700','subtheadClass' => 'px-2','rowClass' => 'border-b hover:bg-slate-700 hover:text-slate-100 hover:border-y-2 hover:border-slate-600 ','tableClass' => 'border-2 border-slate-700']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('table'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['columns' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
        [
            'label' => '#',
            'headerClass' => 'border-slate-600 w-24',
        ],
        [
            'label' => 'Feature name',
            'columnName' => 'feature', // <-- for the arrow
            'headerClass' => 'border-l border-slate-600 w-48',
        ],
        [
            'label' => 'Value',
            'columnName' => 'value',
            'headerClass' => 'border-l border-slate-600 w-32',
        ],
        [
            'label' => 'Actions',
            'headerClass' => 'border-l border-slate-600 w-24',
        ],
    ]),'data' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($this->featureSettings),'captionClass' => 'text-slate-200','sort-direction' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($sortDirection),'sort-column' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($sortColumn),'results' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($featureSettingsAvailable),'maxScrollHeight' => 'max-h-[500px] hidden lg:block pr-1','theadClass' => 'bg-slate-700 border-l border-slate-700','subtheadClass' => 'px-2','rowClass' => 'border-b hover:bg-slate-700 hover:text-slate-100 hover:border-y-2 hover:border-slate-600 ','tableClass' => 'border-2 border-slate-700']); ?>

        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $this->featureSettings; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $featureSetting): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <tr wire:key="<?php echo e('feature-setting-' . $featureSetting->id); ?>"
                class="border-b bg-slate-800 hover:bg-slate-700 border-slate-700">
                <td class="px-2 py-1  text-slate-200 w-24">
                    <?php echo e($loop->iteration); ?>

                </td>
                <td class="border-l border-slate-600 px-2 py-1 text-slate-200 w-48">
                    <?php echo e($featureSetting->feature); ?>

                </td>
                <td class="border-l border-slate-600 px-2 py-1 text-slate-200 w-32">
                    <?php if (isset($component)) { $__componentOriginal1c8f71f73e37bc1aa3cb6e4c4485d6b7 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal1c8f71f73e37bc1aa3cb6e4c4485d6b7 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.boolean-badge','data' => ['value' => ''.e($featureSetting->whereValueEnabled() ? true : false).'','trueStatement' => 'Enabled','falseStatement' => 'Disabled']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('boolean-badge'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['value' => ''.e($featureSetting->whereValueEnabled() ? true : false).'','trueStatement' => 'Enabled','falseStatement' => 'Disabled']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal1c8f71f73e37bc1aa3cb6e4c4485d6b7)): ?>
<?php $attributes = $__attributesOriginal1c8f71f73e37bc1aa3cb6e4c4485d6b7; ?>
<?php unset($__attributesOriginal1c8f71f73e37bc1aa3cb6e4c4485d6b7); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal1c8f71f73e37bc1aa3cb6e4c4485d6b7)): ?>
<?php $component = $__componentOriginal1c8f71f73e37bc1aa3cb6e4c4485d6b7; ?>
<?php unset($__componentOriginal1c8f71f73e37bc1aa3cb6e4c4485d6b7); ?>
<?php endif; ?>
                </td>
                <td class="border-l border-slate-600 px-2 py-1 w-24">
                    <span class="flex justify-end items-center space-x-1">
                        
                        <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('backend.settings.system.feature-settings.view-feature-settings-component', ['featureSetting' => $featureSetting]);

$__html = app('livewire')->mount($__name, $__params, 'view-feature-setting-' . $featureSetting->id, $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                            <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('backend.settings.system.feature-settings.update-feature-settings-component', ['featureSetting' => $featureSetting]);

$__html = app('livewire')->mount($__name, $__params, 'update-feature-setting-' . $featureSetting->id, $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                                <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('backend.settings.system.feature-settings.delete-feature-settings-component', ['featureSetting' => $featureSetting]);

$__html = app('livewire')->mount($__name, $__params, 'delete-feature-setting-' . $featureSetting->id, $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                    </span>

                </td>
            </tr>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal163c8ba6efb795223894d5ffef5034f5)): ?>
<?php $attributes = $__attributesOriginal163c8ba6efb795223894d5ffef5034f5; ?>
<?php unset($__attributesOriginal163c8ba6efb795223894d5ffef5034f5); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal163c8ba6efb795223894d5ffef5034f5)): ?>
<?php $component = $__componentOriginal163c8ba6efb795223894d5ffef5034f5; ?>
<?php unset($__componentOriginal163c8ba6efb795223894d5ffef5034f5); ?>
<?php endif; ?>

    
    <?php if (isset($component)) { $__componentOriginalace4d1b2e22e971ae2a2b53aefed4e19 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalace4d1b2e22e971ae2a2b53aefed4e19 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.table-list','data' => ['data' => $this->featureSettings,'captionMobileClass' => 'text-slate-200','maxScrollHeight' => 'max-h-[500px] lg:hidden pr-1']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('table-list'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['data' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($this->featureSettings),'captionMobileClass' => 'text-slate-200','maxScrollHeight' => 'max-h-[500px] lg:hidden pr-1']); ?>
        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $this->featureSettings; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $featureSetting): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php
                $rowNumber++;
            ?>
            <div wire:key="<?php echo e('feature-setting-2-' . $featureSetting->id); ?>"
                class="p-2 duration-300 ease-in-out rounded-md shadow bg-slate-500 text-slate-50 hover:bg-slate-600 hover:shadow-lg">
                <div class="flex justify-between items-center mb-1 pr-1">
                    <span class="text-sm text-slate-100">Feature:</span>
                    <span class="text-sm text-slate-100"><?php echo e($featureSetting->feature); ?></span>
                </div>
                <div class="flex justify-between items-center mb-1 pr-1">
                    <span class="text-sm text-slate-100">Value:</span>
                    <?php if (isset($component)) { $__componentOriginal1c8f71f73e37bc1aa3cb6e4c4485d6b7 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal1c8f71f73e37bc1aa3cb6e4c4485d6b7 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.boolean-badge','data' => ['value' => ''.e($featureSetting->whereValueEnabled ? true : false).'','trueStatement' => 'Enabled','falseStatement' => 'Disabled']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('boolean-badge'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['value' => ''.e($featureSetting->whereValueEnabled ? true : false).'','trueStatement' => 'Enabled','falseStatement' => 'Disabled']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal1c8f71f73e37bc1aa3cb6e4c4485d6b7)): ?>
<?php $attributes = $__attributesOriginal1c8f71f73e37bc1aa3cb6e4c4485d6b7; ?>
<?php unset($__attributesOriginal1c8f71f73e37bc1aa3cb6e4c4485d6b7); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal1c8f71f73e37bc1aa3cb6e4c4485d6b7)): ?>
<?php $component = $__componentOriginal1c8f71f73e37bc1aa3cb6e4c4485d6b7; ?>
<?php unset($__componentOriginal1c8f71f73e37bc1aa3cb6e4c4485d6b7); ?>
<?php endif; ?>
                </div>
                <div class="flex justify-end items-center space-x-1 mt-2">
                    <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('backend.settings.system.feature-settings.view-feature-settings-component', ['featureSetting' => $featureSetting]);

$__html = app('livewire')->mount($__name, $__params, 'view-2-feature-setting-' . $featureSetting->id, $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                        <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('backend.settings.system.feature-settings.update-feature-settings-component', ['featureSetting' => $featureSetting]);

$__html = app('livewire')->mount($__name, $__params, 'update-2-feature-setting-' . $featureSetting->id, $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                            <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('backend.settings.system.feature-settings.delete-feature-settings-component', ['featureSetting' => $featureSetting]);

$__html = app('livewire')->mount($__name, $__params, 'delete-2-feature-setting-' . $featureSetting->id, $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                </div>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalace4d1b2e22e971ae2a2b53aefed4e19)): ?>
<?php $attributes = $__attributesOriginalace4d1b2e22e971ae2a2b53aefed4e19; ?>
<?php unset($__attributesOriginalace4d1b2e22e971ae2a2b53aefed4e19); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalace4d1b2e22e971ae2a2b53aefed4e19)): ?>
<?php $component = $__componentOriginalace4d1b2e22e971ae2a2b53aefed4e19; ?>
<?php unset($__componentOriginalace4d1b2e22e971ae2a2b53aefed4e19); ?>
<?php endif; ?>

    
    

</div>
<?php /**PATH C:\Users\<USER>\Herd\laraauthstarter\resources\views/livewire/backend/settings/system/feature-settings/show-feature-settings-component.blade.php ENDPATH**/ ?>