<?php

namespace App\Livewire\Backend\Settings\System\AllowedMails;

use Livewire\Component;
use App\Models\AllowedMail;
use App\DTOs\AllowedMailDTO;
use Livewire\Attributes\Locked;
use Livewire\Attributes\Computed;
use App\Facades\Securities\HoneypotFacade;
use App\Enums\AllowedMails\AllowedMailStatusEnum;
use App\Livewire\Forms\Securities\RoadAccessForm;
use App\Facades\AllowedMails\UpdateAllowedMailFacade;
use App\Livewire\Forms\AllowedMails\AllowedMailsForm;
use App\Facades\Securities\OriginHeaderValidatorFacade;

class UpdateAllowedMailsComponent extends Component
{
    #[Locked]
    public AllowedMail $allowedMail;

    public AllowedMailsForm $form;
    public RoadAccessForm $roadAccessForm;
    public $revealUpdateAllowedMailModal = false;


    public function openModalToUpdateAllowedMail()
    {
        $this->resetErrorBag();
        $this->form->resetForm();
        $this->roadAccessForm->generateTrackingTime();
        if ($this->roadAccessForm->checkUserAttempts('UpdateAllowedMail:' . request()->ip(), 6, 300)) {return;}

        $this->form->selectedAllowedMail = $this->allowedMail;
        $this->form->email = $this->allowedMail->email;
        $this->form->description = $this->allowedMail->description;
        $this->form->status = $this->allowedMail->status->value;
        $this->revealUpdateAllowedMailModal = true;
    }

    public function updateAllowedMail()
    {
        $validated = $this->form->validate();
        if ($this->roadAccessForm->checkUserAttempts('UpdateAllowedMail:' . request()->ip(), 6, 300)) {return;}

        OriginHeaderValidatorFacade::validate();
        HoneypotFacade::inspect($this->roadAccessForm);
        $dto = new AllowedMailDTO(...$validated);

        try {
            UpdateAllowedMailFacade::execute($this->allowedMail, $dto);
            $this->dispatch('allowedMailUpdated');
        } catch (\Throwable $th) {
            $this->dispatch('allowedMailUpdateFailed');
        }
    }
    #[Computed]
    public function mailStatusOptions()
    {
        return AllowedMailStatusEnum::getMailStatusOptions();
    }


    public function render()
    {
        return view('livewire.backend.settings.system.allowed-mails.update-allowed-mails-component');
    }
}
