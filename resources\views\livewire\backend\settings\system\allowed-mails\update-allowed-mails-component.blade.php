<div>
    <x-action-buttons :id="$allowedMail->id" editAction="openModalToUpdateAllowedMail" />

    <x-dialog-modal wire:model="revealUpdateAllowedMailModal" :maxWidth="'xl'">
        <x-slot name="content">
            <x-divider nameClass="text-blue-400">
                {{ __('UPDATE ALLOWED MAIL') }}
            </x-divider>

            <x-road-access />
            <x-rate-limiter-message :message="$this->roadAccessForm->road_access_rate_limiter" />

            @if (!$this->roadAccessForm->road_access_rate_limiter)
                <x-lable-input-error id="form.email" type="text" label="Email" model="form.email" inputType="input"
                    value="{{ $form->email }}" :onEnter="$form->getUpdateAllowedMailEvent()" placeholder="Enter email...."
                    modelModifier="live.debounce.400ms" inputClass="w-full" icon="envelope" iconClass="ps-2.5" />

                <x-lable-input-error id="form.description" type="text" label="Description" model="form.description"
                    inputType="textarea" value="{{ $form->description }}" :onEnter="$form->getUpdateAllowedMailEvent()"
                    placeholder="Enter description...." modelModifier="live.debounce.500ms" inputClass="w-full"
                    parentClass="mt-3" />

                <x-lable-select-error parentClass="mt-2" labelClass="w-full text-slate-300"
                    selectClass="font-bold text-green-800" id="form.status" label="Status" model="form.status"
                    placeholder="Select status" :options="$this->mailStatusOptions" :value="$form->status"
                    modelModifier="live.debounce.500ms" />
            @endif

            <div class="sm:flex items-center mt-4">
                <x-button class=" bg-red-500 hover:bg-red-600" wire:click="$toggle('revealUpdateAllowedMailModal')"
                    target="revealUpdateAllowedMailModal" icon='ban'>
                    {{ __('Cancel') }}
                </x-button>

                @if (!$this->roadAccessForm->road_access_rate_limiter)
                    <x-button class="ms-3  bg-green-500 hover:bg-green-600" wire:click="updateAllowedMail"
                        target="updateAllowedMail" :disabled="$form->hasErrors()" icon='cloud-arrow-up'>
                        {{ __('Update') }}
                    </x-button>
                @endif

                <x-action-message class="sm:ms-3 mt-2 sm:mt-0" on="allowedMailUpdated">
                    {{ __('Allowed mail updated successfully.') }}
                </x-action-message>

                <x-action-message class="sm:ms-3 mt-2 sm:mt-0 bg-red-600 text-red-50" on="allowedMailUpdateFailed">
                    {{ __('Error occured while trying to update allowed mail') }}
                </x-action-message>
            </div>
        </x-slot>
    </x-dialog-modal>
</div>
