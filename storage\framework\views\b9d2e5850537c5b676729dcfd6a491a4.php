<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'filters' => [], // An array of filters with 'name', 'label', and 'options'
    'formWidth' => 'max-w-full',
    'results' => false,
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'filters' => [], // An array of filters with 'name', 'label', and 'options'
    'formWidth' => 'max-w-full',
    'results' => false,
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars, $__key, $__value); ?>

<?php
    $numFilters = count($filters);

    // Dynamic class for flex width based on the number of filters
    if ($numFilters == 1) {
        $flexClass = 'w-full';
    } elseif ($numFilters == 2) {
        $flexClass = 'md:w-1/2';
    } elseif ($numFilters == 3) {
        $flexClass = 'md:w-1/3';
    } elseif ($numFilters == 4) {
        $flexClass = 'md:w-1/4';
    } else {
        $flexClass = 'md:w-1/3';
    }

    // Decide whether to keep filters horizontally or stack them
    $filtersDirectionClass = $numFilters < 3 ? 'lg:flex-row' : '';
?>


    <form class="<?php echo e($formWidth); ?> mx-auto my-4">
        <div class="items-center md:flex <?php echo e($filtersDirectionClass); ?> md:space-x-4 space-y-4 md:space-y-0">

            <!-- Dynamic Filters (All Select) -->
            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $filters; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $filter): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="<?php echo e($flexClass); ?>">
                    <label for="<?php echo e($filter['name']); ?>" class="sr-only"><?php echo e($filter['label']); ?></label>
                    <select id="<?php echo e($filter['name']); ?>" wire:model.live="<?php echo e($results ? $filter['name'] : ''); ?>"
                        <?php echo e(empty($filter['options']) ? 'disabled' : ''); ?>

                        class="block w-full px-4 py-2 text-sm border rounded-md text-slate-200 focus:ring-amber-500 focus:border-amber-500
                    bg-slate-700 border-slate-600 placeholder-slate-200">
                        <option value=""><?php echo e($filter['label']); ?></option>
                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $filter['options']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($key); ?>"><?php echo e($value); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                    </select>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
        </div>
    </form>

<?php /**PATH C:\Users\<USER>\Herd\laraauthstarter\resources\views/components/filter.blade.php ENDPATH**/ ?>