<?php

namespace App\Repositories\FeatureSettings;

use Illuminate\Support\Facades\Cache;
use App\Contracts\Repos\FeatureSettingsRepos\CountFeatureSettingsRepositoryInterface;

final class CachedCountFeatureSettingsRepository implements CountFeatureSettingsRepositoryInterface
{
    public function __construct(private CountFeatureSettingsRepositoryInterface $countFeatureSettingsRepository) {}

    public function handle(): int
    {
        $key   = 'feature_settings:count';
        $fresh = 300; // 5 minutes
        $stale = 900; // 15 minutes

        return Cache::flexible($key, [$fresh, $stale], function () {
            return $this->countFeatureSettingsRepository->handle();
        });
    }
}
