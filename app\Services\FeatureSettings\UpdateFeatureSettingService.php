<?php

namespace App\Services\FeatureSettings;

use App\Models\FeatureSetting;
use App\DTOs\FeatureSettingDTO;
use App\Actions\FeatureSettings\UpdateFeatureSettingAction;
use App\Contracts\FeatureSettings\UpdateFeatureSettingServiceInterface;

class UpdateFeatureSettingService implements UpdateFeatureSettingServiceInterface
{
    public function execute(FeatureSetting $selectedFeatureSetting, FeatureSettingDTO $dto): FeatureSetting
    {
        return UpdateFeatureSettingAction::handle($selectedFeatureSetting, $dto->toArray());
    }
}
