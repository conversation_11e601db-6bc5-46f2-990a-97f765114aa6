<?php

namespace App\Repositories\AllowedMails;

use App\Models\AllowedMail;
use Illuminate\Support\Collection;
use App\Contracts\Repos\AllowedMailsRepos\GetAllowedMailsRepositoryInterface;

final class GetAllowedMailsRepository implements GetAllowedMailsRepositoryInterface
{
    public function handle(int $limit = 10, array $selectedColumns = ['*']): Collection
    {
        return AllowedMail::select($selectedColumns)
            ->orderByIdDesc()
            ->limit($limit)
            ->get();
    }
}
