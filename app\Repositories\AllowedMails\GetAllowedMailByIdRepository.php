<?php

namespace App\Repositories\AllowedMails;

use App\Models\AllowedMail;
use App\Contracts\Repos\AllowedMailsRepos\GetAllowedMailByIdRepositoryInterface;

final class GetAllowedMailByIdRepository implements GetAllowedMailByIdRepositoryInterface
{
    public function handle(int $id, array $selectedColumns = ['*']): ?AllowedMail
    {
        return AllowedMail::select($selectedColumns)->find($id);
    }
}
