<?php

namespace App\Builders;

use Illuminate\Database\Eloquent\Builder;

class FeatureSettingBuilder extends Builder
{
    public function orderByIdAsc(): self
    {
        return $this->orderBy('id', 'asc');
    }

    public function orderByIdDesc(): self
    {
        return $this->orderBy('id', 'desc');
    }

    public function orderByFeatureAsc(): self
    {
        return $this->orderBy('feature', 'asc');
    }

    public function orderByFeatureDesc(): self
    {
        return $this->orderBy('feature', 'desc');
    }

    public function orderByValueAsc(): self
    {
        return $this->orderBy('value', 'asc');
    }

    public function orderByValueDesc(): self
    {
        return $this->orderBy('value', 'desc');
    }

    public function whereFeature(string $feature): self
    {
        return $this->where('feature', $feature);
    }

    public function whereValue(int $value): self
    {
        return $this->where('value', $value);
    }

    public function whereValueEnabled(): self
    {
        return $this->where('value', 2);
    }

    public function whereValueDisabled(): self
    {
        return $this->where('value', 1);
    }
}
