@php
    $untrustedHeadersPagination =
        $this->untrustedHeaders->isNotEmpty() && $this->totalUntrustedHeaders > 5 ? true : false;
    $untrustedHeadersAvailable = $untrustedHeadersPagination;
    $rowNumber = 0; // Initialize row number
@endphp
<div>
    <livewire:backend.settings.system.headers.create-untrusted-headers-component />

    {{-- Filters and search --}}
    <div>
        <x-filter :filters="[
            [
                'name' => 'filterHeadersByName',
                'label' => 'Choose by name',
                'options' => $this->headerNamesOptions,
            ],
        ]" :results="$untrustedHeadersAvailable" />
    </div>

    {{-- Search --}}
    <x-search :results="$untrustedHeadersAvailable" searchProperty="headersSearch"
        searchPlaceholder="Search by header's name, pattern and description...." formWidth="max-w-full my-3" />

    {{-- Table --}}
    <x-table :columns="[
        [
            'label' => '#',
            'headerClass' => 'border-slate-600',
        ],
        [
            'label' => 'Header name',
            'columnName' => 'header_type', // <-- for the arrow
            'headerClass' => 'border-l border-slate-600 w-32',
        ],
        [
            'label' => 'Pattern',
            'columnName' => 'pattern',
            'headerClass' => 'border-l border-slate-600 w-32',
        ],
        [
            'label' => 'Description',
            'headerClass' => 'border-l border-slate-600 line-clamp-1',
        ],
        [
            'label' => 'Actions',
            'headerClass' => 'border-l border-slate-600 w-20',
        ],
    ]" :data="$this->untrustedHeaders" captionClass="text-slate-200"
        :sort-direction="$sortDirection" :sort-column="$sortColumn" :results="$untrustedHeadersAvailable"
        maxScrollHeight="max-h-[400px] hidden lg:block pr-1" theadClass="bg-slate-700 border-l border-slate-700"
        subtheadClass="px-2"
        rowClass="border-b hover:bg-slate-700 hover:text-slate-100 hover:border-y-2 hover:border-slate-600 "
        tableClass="border-2 border-slate-700">

        @foreach ($this->untrustedHeaders as $header)
            <tr wire:key="{{ 'header-' . $header->id }}"
                class="border-b bg-slate-800 hover:bg-slate-700 border-slate-700">
                <td class="px-2 py-1  text-slate-200">
                    {{ $loop->iteration }}
                </td>
                <td class="border-l border-slate-600 px-2 py-1 text-slate-200 w-32">
                    {{ $header->header_type->label() }}
                </td>
                <td class="border-l border-slate-600 px-2 py-1 text-slate-200 w-32">
                    {{ $header->pattern }}
                </td>
                <td class="border-l border-slate-600 px-2 py-1 text-slate-200  line-clamp-1">
                    {{ $header->description }}
                </td>

                <td class="border-l border-slate-600 px-2 py-1 w-20">
                    <span class="flex justify-end items-center space-x-1">
                        {{-- Action buttons --}}
                        <livewire:backend.settings.system.headers.view-untrusted-headers-component :$header
                            :key="'view-header-' . $header->id">
                            <livewire:backend.settings.system.headers.update-untrusted-headers-component :$header
                                :key="'update-header-' . $header->id">
                                <livewire:backend.settings.system.headers.delete-untrusted-headers-component :$header
                                    :key="'delete-header-' . $header->id">
                    </span>

                </td>
            </tr>
        @endforeach
    </x-table>

    {{-- Table List --}}
    <x-table-list :data="$this->untrustedHeaders" captionMobileClass="text-slate-200" maxScrollHeight="max-h-[500px] lg:hidden pr-1">
        @foreach ($this->untrustedHeaders as $header)
            @php
                $rowNumber++;
            @endphp
            <div wire:key="{{ 'header-2-' . $header->id }}"
                class="p-2 duration-300 ease-in-out rounded-md shadow bg-slate-500 text-slate-50 hover:bg-slate-600 hover:shadow-lg">
                <div class="flex justify-between items-center mb-1 pr-1">
                    <span class="text-sm text-slate-100">Name:</span>
                    <span class="text-sm text-slate-100">{{ $header->header_type->label() }}</span>
                </div>
                <div class="flex justify-between items-center mb-1 pr-1">
                    <span class="text-sm text-slate-100">Pattern:</span>
                    <span class="text-sm text-slate-100">{{ $header->pattern }}</span>
                </div>
                <div class="mb-1 block bg-slate-800 rounded px-1.5 py-1">
                    <div class=" text-amber-200">Description:</div>
                    <div class="text-sm text-slate-200 line-clamp-2">{{ $header->description }}</div>
                </div>
                <div class="flex justify-between items-center">
                    <span class="flex justify-end items-center space-x-1">
                        {{-- Action buttons --}}
                        <livewire:backend.settings.system.headers.view-untrusted-headers-component :$header
                            :key="'view-2-header-' . $header->id">
                            <livewire:backend.settings.system.headers.update-untrusted-headers-component :$header
                                :key="'update-2-header-' . $header->id">
                                <livewire:backend.settings.system.headers.delete-untrusted-headers-component :$header
                                    :key="'delete-2-header-' . $header->id">
                    </span>

                    @if ($rowNumber >= 1)
                        <span
                            class="text-right rounded-full px-2 bg-slate-950 text-slate-50 text-xs">{{ $rowNumber }}</span>
                    @endif
                </div>
            </div>
        @endforeach
    </x-table-list>

    {{-- Pagination --}}
    <x-paginator :pages="$this->headersPages" :totalRecords="$this->totalUntrustedHeaders" selectClass="text-slate-500" paginateRecords="headersPaginate"
        :results="$untrustedHeadersAvailable" :paginationEnabled="$untrustedHeadersPagination" />
</div>
