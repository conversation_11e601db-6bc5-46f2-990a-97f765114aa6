<div>
    {{-- Show Permissions Component --}}
    <div class="card">
        <div class="card-header">
            <h5 class="card-title">Permissions Management</h5>
        </div>
        <div class="card-body">
            {{-- Search and filters will go here --}}
            <div class="row mb-3">
                <div class="col-md-6">
                    <input type="text" wire:model.live="permissionsSearch" class="form-control" placeholder="Search permissions...">
                </div>
                <div class="col-md-6">
                    <select wire:model.live="permissionsPaginate" class="form-select">
                        <option value="5">5 per page</option>
                        <option value="10">10 per page</option>
                        <option value="25">25 per page</option>
                        <option value="50">50 per page</option>
                    </select>
                </div>
            </div>

            {{-- Permissions table will go here --}}
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th wire:click="doSort('id')" style="cursor: pointer;">ID</th>
                            <th wire:click="doSort('name')" style="cursor: pointer;">Name</th>
                            <th wire:click="doSort('slug')" style="cursor: pointer;">Slug</th>
                            <th wire:click="doSort('created_at')" style="cursor: pointer;">Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {{-- Permission rows will be populated here --}}
                        <tr>
                            <td colspan="5" class="text-center text-muted">
                                No permissions found. Permission model needs to be implemented.
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            {{-- Pagination will go here --}}
        </div>
    </div>
</div>
