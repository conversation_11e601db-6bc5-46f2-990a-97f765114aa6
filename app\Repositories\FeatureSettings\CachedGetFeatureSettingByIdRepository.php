<?php

namespace App\Repositories\FeatureSettings;

use App\Models\FeatureSetting;
use Illuminate\Support\Facades\Cache;
use App\Contracts\Repos\FeatureSettingsRepos\GetFeatureSettingByIdRepositoryInterface;

final class CachedGetFeatureSettingByIdRepository implements GetFeatureSettingByIdRepositoryInterface
{
    public function __construct(private GetFeatureSettingByIdRepositoryInterface $getFeatureSettingByIdRepository) {}

    public function handle(int $id, array $selectedColumns = ['*']): ?FeatureSetting
    {
        $fresh = 1800; // 30 minutes
        $stale = 3600; // 1 hour

        $columns = $selectedColumns;
        sort($columns); // normalize to avoid order mismatch
        $key = 'feature_setting_id:' . $id . ':' . implode(',', $columns);

        return Cache::flexible($key, [$fresh, $stale], function () use ($id, $selectedColumns) {
            return $this->getFeatureSettingByIdRepository->handle($id, $selectedColumns);
        });
    }
}
