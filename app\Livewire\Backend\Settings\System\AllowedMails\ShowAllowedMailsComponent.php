<?php

namespace App\Livewire\Backend\Settings\System\AllowedMails;

use Livewire\Component;
use App\Models\AllowedMail;
use Livewire\Attributes\On;
use Livewire\Attributes\Computed;
use App\Supports\PaginatorSupport;
use App\Enums\AllowedMails\AllowedMailStatusEnum;
use App\Facades\Repos\AllowedMails\CountAllowedMailsFacade;
use App\Traits\Defaults\HasSortables;

class ShowAllowedMailsComponent extends Component
{
    use HasSortables;

    public $allowedMailsSearch = '';
    public $allowedMailsPaginate = 5;
    public $filterMailsByStatus = '';

    public function doSort(string $column)
    {
        $this->sortingDirection($column);
    }


    #[Computed]
    public function allowedMails()
    {
        $selectedColumns = ['id', 'email', 'status', 'description'];
        $searchTerm = trim('%' . $this->allowedMailsSearch . '%');
        $filterTerms = [
            'filterMailsByStatus' => $this->filterMailsByStatus,
        ];

        $query = AllowedMail::query();
        $query = $query->select($selectedColumns);
        $query = $query->search($searchTerm);
        $query = $query->filter($filterTerms);
        $query = $this->sortBy($query, $this->sortColumn, $this->sortDirection);
        $query = $query->paginate($this->allowedMailsPaginate);

        return $query;
    }

    #[Computed]
    public function totalAllowedMails()
    {
        return CountAllowedMailsFacade::handle();
    }

    #[Computed]
    public function allowedMailsPages(): array
    {
        return PaginatorSupport::generatePages($this->totalAllowedMails);
    }

    #[Computed]
    public function mailStatusOptions()
    {
        return AllowedMailStatusEnum::getMailStatusOptions();
    }

    #[On(['allowedMailCreated', 'allowedMailUpdated', 'allowedMailDeleted'])]
    public function render()
    {
        return view('livewire.backend.settings.system.allowed-mails.show-allowed-mails-component');
    }

    public function placeholder()
    {
        return view('placeholders.lazy-spin-loader');
    }
}
