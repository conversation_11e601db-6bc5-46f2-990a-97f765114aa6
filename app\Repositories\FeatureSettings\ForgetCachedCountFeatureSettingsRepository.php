<?php

namespace App\Repositories\FeatureSettings;

use Illuminate\Support\Facades\Cache;
use App\Contracts\Repos\FeatureSettingsRepos\ForgetCachedCountFeatureSettingsRepositoryInterface;

final class ForgetCachedCountFeatureSettingsRepository implements ForgetCachedCountFeatureSettingsRepositoryInterface
{
    public function handle(): void
    {
        $key = 'feature_settings:count';
        Cache::forget($key);
    }
}
