<?php

namespace App\Repositories\AllowedMails;

use App\Models\AllowedMail;
use Illuminate\Support\Facades\Cache;
use App\Contracts\Repos\AllowedMailsRepos\GetAllowedMailByIdRepositoryInterface;

class CachedGetAllowedMailByIdRepository implements GetAllowedMailByIdRepositoryInterface
{
    public function __construct(private GetAllowedMailByIdRepositoryInterface $getAllowedMailByIdRepository) {}

    public function handle(int $id, array $selectedColumns = ['*']): ?AllowedMail
    {
        $fresh = 1800; // 30 minutes
        $stale = 3600; // 1 hour

        $columns = $selectedColumns;
        sort($columns); // normalize to avoid order mismatch
        $key = 'allowed_mail_id:' . $id . ':' . implode(',', $columns);

        return Cache::flexible($key, [$fresh, $stale], function () use ($id, $selectedColumns) {
            return $this->getAllowedMailByIdRepository->handle($id, $selectedColumns);
        });
    }
}
