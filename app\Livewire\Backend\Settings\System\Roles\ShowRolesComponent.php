<?php

namespace App\Livewire\Backend\Settings\System\Roles;

use App\Models\Role;
use Livewire\Component;
use Livewire\Attributes\On;
use Livewire\Attributes\Url;
use Livewire\WithPagination;
use Livewire\Attributes\Computed;
use App\Enums\Roles\RoleTypesEnum;
use App\Supports\PaginatorSupport;
use App\Traits\Defaults\HasSortables;
use App\Facades\Repos\Roles\CountRolesFacade;

class ShowRolesComponent extends Component
{
    use WithPagination;
    use HasSortables;
    #[Url]
    public $rolesSearch = '';
    #[Url]
    public $rolesPaginate = 5;
    #[Url]
    public $filterRolesByType = '';


    public function doSort(string $column)
    {
        $this->sortingDirection($column);
    }

    #[Computed]
    public function roles()
    {
        $selectedColumns = ['id', 'name', 'type', 'description'];
        $searchTerm = trim('%' . $this->rolesSearch . '%');
        $filterTerms = [
            'filterByType' => $this->filterRolesByType,
        ];

        $query = Role::query();
        $query = $query->select($selectedColumns);
        $query = $query->search($searchTerm);
        $query = $query->filter($filterTerms);
        $query = $this->sortBy($query, $this->sortColumn, $this->sortDirection);
        $query = $query->paginate($this->rolesPaginate);

        return $query;
    }

    #[Computed]
    public function roleTypeOptions()
    {
        return RoleTypesEnum::getRoleTypeOptions();
    }

    #[Computed]
    public function totalRoles()
    {
        return CountRolesFacade::handle();
    }

    #[Computed()]
    public function rolesPages(): array
    {
        return PaginatorSupport::generatePages($this->totalRoles);
    }

    #[On(['roleCreated', 'roleUpdated', 'roleDeleted'])]
    public function render()
    {

        return view('livewire.backend.settings.system.roles.show-roles-component');
    }

    public function placeholder()
    {
        return view('placeholders.lazy-spin-loader');
    }
}
