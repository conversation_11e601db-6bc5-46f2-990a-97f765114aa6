<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'columnName',
    'sortDirection',
    'sortColumn'
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'columnName',
    'sortDirection',
    'sortColumn'
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars, $__key, $__value); ?>

<!--[if BLOCK]><![endif]--><?php if($sortColumn !== $columnName): ?>
    <i class="fa-solid fa-up-down text-sm text-green-100"></i>
<?php else: ?>
    <!--[if BLOCK]><![endif]--><?php if($sortDirection === 'ASC'): ?>
        <i class="fa-solid fa-arrow-up text-base text-green-500"></i>
    <?php elseif($sortDirection === 'DESC'): ?>
        <i class="fa-solid fa-arrow-down text-base text-green-500"></i>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
<?php endif; ?><!--[if ENDBLOCK]><![endif]-->
<?php /**PATH C:\Users\<USER>\Herd\laraauthstarter\resources\views/components/sort-columns.blade.php ENDPATH**/ ?>