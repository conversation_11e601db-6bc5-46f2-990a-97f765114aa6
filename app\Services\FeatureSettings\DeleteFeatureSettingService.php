<?php

namespace App\Services\FeatureSettings;

use App\Models\FeatureSetting;
use App\Actions\FeatureSettings\DeleteFeatureSettingAction;
use App\Contracts\FeatureSettings\DeleteFeatureSettingServiceInterface;

class DeleteFeatureSettingService implements DeleteFeatureSettingServiceInterface
{
    public function execute(FeatureSetting $selectedFeatureSetting): bool
    {
        if (!$selectedFeatureSetting) {
            return false;
        }
        return DeleteFeatureSettingAction::handle($selectedFeatureSetting);
    }
}
