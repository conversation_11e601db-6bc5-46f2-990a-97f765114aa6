<?php

namespace App\Repositories\AllowedMails;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use App\Contracts\Repos\AllowedMailsRepos\GetAllowedMailsRepositoryInterface;

class CachedGetAllowedMailsRepository implements GetAllowedMailsRepositoryInterface
{
    public function __construct(private GetAllowedMailsRepositoryInterface $getAllowedMailsRepository) {}

    public function handle(int $limit = 10, array $selectedColumns = ['*']): Collection
    {
        $fresh = 300; // 5 minutes
        $stale = 900; // 15 minutes

        $columns = $selectedColumns;
        sort($columns);
        $key = 'allowed_mails_list:' . $limit . ':' . implode(',', $columns);

        return Cache::flexible($key, [$fresh, $stale], function () use ($limit, $selectedColumns) {
            return $this->getAllowedMailsRepository->handle($limit, $selectedColumns);
        });
    }
}
