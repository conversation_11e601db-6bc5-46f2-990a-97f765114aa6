<?php

namespace Database\Seeders;

use App\Models\AllowedMail;
use Illuminate\Database\Seeder;
use App\Enums\Roles\RoleIdsEnum;
use App\Enums\AllowedMails\AllowedMailStatusEnum;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class AllowedMailSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
                $data = [];

        foreach (RoleIdsEnum::cases() as $role) {
            $data[] = [
                'email'       => strtolower(str_replace(' ', '.', $role->label())) . '@example.com',
                'status'      => AllowedMailStatusEnum::AUTHORISED->value,
                'description' => ucfirst($role->label()),
                'created_at'  => now(),
                'updated_at'  => now(),
            ];
        }

        AllowedMail::insert($data);
    }
}
