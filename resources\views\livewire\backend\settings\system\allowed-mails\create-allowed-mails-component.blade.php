<div>
    <x-button class="bg-slate-600 hover:bg-green-600" wire:click="openModalToCreateAllowedMail"
        target="openModalToCreateAllowedMail" icon="circle-plus">
        {{ __('Add Allowed Mail') }}
    </x-button>

    <x-dialog-modal wire:model="revealCreateAllowedMailModal" :maxWidth="'xl'">
        <x-slot name="content">
            <x-divider nameClass="text-blue-400">
                {{ __('CREATE ALLOWED MAIL') }}
            </x-divider>

            <x-road-access />
            <x-rate-limiter-message :message="$this->roadAccessForm->road_access_rate_limiter" />

            @if (!$this->roadAccessForm->road_access_rate_limiter)
                <x-lable-input-error id="form.email" type="text" label="Email" model="form.email" inputType="input"
                    value="{{ $form->email }}" :onEnter="$form->getCreateAllowedMailEvent()" placeholder="Enter email...."
                    modelModifier="live.debounce.400ms" inputClass="w-full" icon="envelope" iconClass="ps-2.5" />

                <x-lable-input-error id="form.description" type="text" label="Description" model="form.description"
                    inputType="textarea" value="{{ $form->description }}" :onEnter="$form->getCreateAllowedMailEvent()"
                    placeholder="Enter description...." modelModifier="live.debounce.500ms" inputClass="w-full"
                    parentClass="mt-3" />

                <x-lable-select-error parentClass="mt-2" labelClass="w-full text-slate-300"
                    selectClass="font-bold text-green-800" id="form.status" label="Status" model="form.status"
                    placeholder="Select status" :options="$this->mailStatusOptions" :value="$form->status"
                    modelModifier="live.debounce.500ms" />
            @endif

            <div class="sm:flex items-center mt-4">
                <x-button class=" bg-red-500 hover:bg-red-600" wire:click="$toggle('revealCreateAllowedMailModal')"
                    target="revealCreateAllowedMailModal" icon='ban'>
                    {{ __('Cancel') }}
                </x-button>

                @if (!$this->roadAccessForm->road_access_rate_limiter)
                    <x-button class="ms-3  bg-green-500 hover:bg-green-600" wire:click="createAllowedMail"
                        target="createAllowedMail" :disabled="$form->hasErrors()" icon='cloud-arrow-up'>
                        {{ __('Create') }}
                    </x-button>
                @endif
                <x-action-message class="sm:ms-3 mt-2 sm:mt-0" on="allowedMailCreated">
                    {{ __('Allowed mail created successfully.') }}
                </x-action-message>

                <x-action-message class="sm:ms-3 mt-2 sm:mt-0 bg-red-600 text-red-50" on="allowedMailCreationFailed">
                    {{ __('Error occured while trying to create allowed mail') }}
                </x-action-message>
            </div>
        </x-slot>
    </x-dialog-modal>
</div>
