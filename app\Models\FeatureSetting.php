<?php

namespace App\Models;

use App\Casts\TransformStringCast;
use App\Builders\FeatureSettingBuilder;
use App\Traits\Defaults\HasFilterables;
use App\Traits\Defaults\HasSearchables;
use Illuminate\Database\Eloquent\Model;
use App\Enums\FeatureSettings\FeatureSettingValueEnum;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class FeatureSetting extends Model
{
    /** @use HasFactory<\Database\Factories\FeatureSettingFactory> */
    use HasFactory;
    use HasSearchables;
    use HasFilterables;

    protected $fillable = [
        'feature',
        'value',
    ];

    protected $searchable = [
        'feature',
    ];

    protected $filterable = [
        'filterFeatureSettingByValue' => 'value',
    ];

    protected $casts = [
        'id' => 'integer',
        'feature' => TransformStringCast::class . ':strtolower',
        'value' => FeatureSettingValueEnum::class,
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function newEloquentBuilder($query): Builder
    {
        return new FeatureSettingBuilder($query);
    }


}
