<div>
    <?php if (isset($component)) { $__componentOriginalf9332b595ad3d3a806f9da4dda8769dd = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf9332b595ad3d3a806f9da4dda8769dd = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.action-buttons','data' => ['id' => $featureSetting->id,'viewAction' => 'openModalToViewFeatureSetting']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('action-buttons'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['id' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($featureSetting->id),'viewAction' => 'openModalToViewFeatureSetting']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf9332b595ad3d3a806f9da4dda8769dd)): ?>
<?php $attributes = $__attributesOriginalf9332b595ad3d3a806f9da4dda8769dd; ?>
<?php unset($__attributesOriginalf9332b595ad3d3a806f9da4dda8769dd); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf9332b595ad3d3a806f9da4dda8769dd)): ?>
<?php $component = $__componentOriginalf9332b595ad3d3a806f9da4dda8769dd; ?>
<?php unset($__componentOriginalf9332b595ad3d3a806f9da4dda8769dd); ?>
<?php endif; ?>

    <?php if (isset($component)) { $__componentOriginal49bd1c1dd878e22e0fb84faabf295a3f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal49bd1c1dd878e22e0fb84faabf295a3f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dialog-modal','data' => ['wire:model' => 'revealViewFeatureSettingModal','maxWidth' => 'xl']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dialog-modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['wire:model' => 'revealViewFeatureSettingModal','maxWidth' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute('xl')]); ?>
         <?php $__env->slot('content', null, []); ?> 
            <?php if (isset($component)) { $__componentOriginal73a6b0261bc3d280b80775663eef6108 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal73a6b0261bc3d280b80775663eef6108 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.divider','data' => ['nameClass' => 'text-green-400']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('divider'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['nameClass' => 'text-green-400']); ?>
                <?php echo e(__('VIEW FEATURE SETTING')); ?>

             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal73a6b0261bc3d280b80775663eef6108)): ?>
<?php $attributes = $__attributesOriginal73a6b0261bc3d280b80775663eef6108; ?>
<?php unset($__attributesOriginal73a6b0261bc3d280b80775663eef6108); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal73a6b0261bc3d280b80775663eef6108)): ?>
<?php $component = $__componentOriginal73a6b0261bc3d280b80775663eef6108; ?>
<?php unset($__componentOriginal73a6b0261bc3d280b80775663eef6108); ?>
<?php endif; ?>

            <div class="space-y-4 mt-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-slate-300">Feature Name</label>
                        <div class="mt-1 p-2 bg-slate-700 rounded-md text-slate-100">
                            <?php echo e($featureSetting->feature); ?>

                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-slate-300">Value</label>
                        <div class="mt-1 p-2 bg-slate-700 rounded-md">
                            <?php if (isset($component)) { $__componentOriginal1c8f71f73e37bc1aa3cb6e4c4485d6b7 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal1c8f71f73e37bc1aa3cb6e4c4485d6b7 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.boolean-badge','data' => ['value' => ''.e($featureSetting->value->value === 2 ? true : false).'','trueStatement' => 'Enabled','falseStatement' => 'Disabled']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('boolean-badge'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['value' => ''.e($featureSetting->value->value === 2 ? true : false).'','trueStatement' => 'Enabled','falseStatement' => 'Disabled']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal1c8f71f73e37bc1aa3cb6e4c4485d6b7)): ?>
<?php $attributes = $__attributesOriginal1c8f71f73e37bc1aa3cb6e4c4485d6b7; ?>
<?php unset($__attributesOriginal1c8f71f73e37bc1aa3cb6e4c4485d6b7); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal1c8f71f73e37bc1aa3cb6e4c4485d6b7)): ?>
<?php $component = $__componentOriginal1c8f71f73e37bc1aa3cb6e4c4485d6b7; ?>
<?php unset($__componentOriginal1c8f71f73e37bc1aa3cb6e4c4485d6b7); ?>
<?php endif; ?>
                        </div>
                    </div>

                </div>
            </div>

            <div class="flex justify-end mt-6">
                <?php if (isset($component)) { $__componentOriginald0f1fd2689e4bb7060122a5b91fe8561 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.button','data' => ['class' => 'bg-red-600 hover:bg-red-700','wire:click' => '$toggle(\'revealViewFeatureSettingModal\')','target' => 'revealViewFeatureSettingModal','icon' => 'times']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'bg-red-600 hover:bg-red-700','wire:click' => '$toggle(\'revealViewFeatureSettingModal\')','target' => 'revealViewFeatureSettingModal','icon' => 'times']); ?>
                    <?php echo e(__('Close')); ?>

                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561)): ?>
<?php $attributes = $__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561; ?>
<?php unset($__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald0f1fd2689e4bb7060122a5b91fe8561)): ?>
<?php $component = $__componentOriginald0f1fd2689e4bb7060122a5b91fe8561; ?>
<?php unset($__componentOriginald0f1fd2689e4bb7060122a5b91fe8561); ?>
<?php endif; ?>
            </div>
         <?php $__env->endSlot(); ?>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal49bd1c1dd878e22e0fb84faabf295a3f)): ?>
<?php $attributes = $__attributesOriginal49bd1c1dd878e22e0fb84faabf295a3f; ?>
<?php unset($__attributesOriginal49bd1c1dd878e22e0fb84faabf295a3f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal49bd1c1dd878e22e0fb84faabf295a3f)): ?>
<?php $component = $__componentOriginal49bd1c1dd878e22e0fb84faabf295a3f; ?>
<?php unset($__componentOriginal49bd1c1dd878e22e0fb84faabf295a3f); ?>
<?php endif; ?>
</div>
<?php /**PATH C:\Users\<USER>\Herd\laraauthstarter\resources\views/livewire/backend/settings/system/feature-settings/view-feature-settings-component.blade.php ENDPATH**/ ?>