<?php

namespace App\Livewire\Backend\Settings\System\Headers;

use Livewire\Component;
use Livewire\Attributes\On;
use Livewire\Attributes\Url;
use Livewire\WithPagination;
use App\Models\UntrustedHeader;
use Livewire\Attributes\Computed;
use App\Supports\PaginatorSupport;
use App\Traits\Defaults\HasSortables;
use App\Enums\UntrustedHeaders\UntrustedHeaderNamesEnum;
use App\Facades\Repos\UntrustedHeaders\CountUntrustedHeadersFacade;

class ShowUntrustedHeadersComponent extends Component
{
    use WithPagination;
    use HasSortables;

    #[Url]
    public $headersSearch = '';
    #[Url]
    public $headersPaginate = 5;
    #[Url]
    public $filterHeadersByName = '';


    public function doSort(string $column)
    {
        $this->sortingDirection($column);
    }


    #[Computed]
    public function untrustedHeaders()
    {
        $selectedColumns = ['id', 'header_type', 'pattern', 'description'];
        $searchTerm = trim('%' . $this->headersSearch . '%');
        $filterTerms = [
            'filterHeadersByName' => $this->filterHeadersByName,
        ];

        $query = UntrustedHeader::query();
        $query = $query->select($selectedColumns);
        $query = $query->search($searchTerm);
        $query = $query->filter($filterTerms);
        $query = $this->sortBy($query, $this->sortColumn, $this->sortDirection);
        $query = $query->paginate($this->headersPaginate);

        return $query;
    }

    #[Computed]
    public function headerNamesOptions()
    {
        return UntrustedHeaderNamesEnum::getHeaderTypeOptions();
    }

    #[Computed]
    public function totalUntrustedHeaders()
    {
        return CountUntrustedHeadersFacade::handle();
    }

    #[Computed]
    public function headersPages(): array
    {
        return PaginatorSupport::generatePages($this->totalUntrustedHeaders);
    }


    #[On(['untrustedHeaderCreated', 'untrustedHeaderUpdated', 'untrustedHeaderDeleted'])]
    public function render()
    {
        // dd(request()->headers->all());
        return view('livewire.backend.settings.system.headers.show-untrusted-headers-component');
    }

    public function placeholder()
    {
        return view('placeholders.lazy-spin-loader');
    }
}
