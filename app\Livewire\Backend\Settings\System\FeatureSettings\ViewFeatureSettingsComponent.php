<?php

namespace App\Livewire\Backend\Settings\System\FeatureSettings;

use Livewire\Component;
use App\Models\FeatureSetting;
use Livewire\Attributes\Locked;

class ViewFeatureSettingsComponent extends Component
{
    #[Locked]
    public FeatureSetting $featureSetting;
    public $revealViewFeatureSettingModal = false;

    public function openModalToViewFeatureSetting()
    {
        $this->revealViewFeatureSettingModal = true;
    }

    public function render()
    {
        return view('livewire.backend.settings.system.feature-settings.view-feature-settings-component');
    }
}
