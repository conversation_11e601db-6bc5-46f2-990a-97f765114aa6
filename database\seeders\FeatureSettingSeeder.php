<?php

namespace Database\Seeders;

use App\Models\FeatureSetting;
use Illuminate\Database\Seeder;
use App\Enums\FeatureSettings\FeatureSettingValueEnum;

class FeatureSettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $features = [
            'system',
            'login',
            'register',
            'forgot-password',
            'reset-password',
            'email-verification',
            'two-factor-authentication',
            'oauth-login',
            'password-reset',
            'user-registration',
        ];

        $settings = [];

        foreach ($features as $feature) {
            $settings[] = [
                'feature' => $feature,
                'value' => FeatureSettingValueEnum::ENABLED->value,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        FeatureSetting::insert($settings);
    }
}
