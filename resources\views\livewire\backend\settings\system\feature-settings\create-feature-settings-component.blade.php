<div>
    <x-button class="bg-slate-600 hover:bg-green-600" wire:click="openModalToCreateFeatureSetting" target="openModalToCreateFeatureSetting"
        icon="circle-plus">
        {{ __('ADD Feature Setting') }}
    </x-button>

    <x-dialog-modal wire:model="revealCreateFeatureSettingsModal" :maxWidth="'xl'">
        <x-slot name="content">
            <x-divider nameClass="text-blue-400">
                {{ __('ADD A FEATURE SETTING') }}
            </x-divider>

            <x-road-access />
            <x-rate-limiter-message :message="$this->roadAccessForm->road_access_rate_limiter" />

            @if (!$this->roadAccessForm->road_access_rate_limiter)
                <x-lable-input-error id="form.feature" type="text" label="Feature name" model="form.feature" inputType="input"
                    value="{{ $form->feature }}" :onEnter="$form->getCreateFeatureSettingEvent()" placeholder="Enter feature name...."
                    modelModifier="live.debounce.400ms" inputClass="w-full" icon="cog" iconClass="ps-2.5" />

                <x-lable-select-error parentClass="mt-2" labelClass="w-full text-slate-300"
                    selectClass="font-bold text-green-800" id="form.value" label="Feature value" model="form.value"
                    placeholder="Select feature value" :options="$this->featureSettingValueOptions" :value="$form->value"
                    modelModifier="live.debounce.500ms" />
            @endif

            <div class="sm:flex items-center mt-4">
                <x-button class=" bg-red-500 hover:bg-red-600" wire:click="$toggle('revealCreateFeatureSettingsModal')"
                    target="revealCreateFeatureSettingsModal" icon='ban'>
                    {{ __('Cancel') }}
                </x-button>

                @if (!$this->roadAccessForm->road_access_rate_limiter)
                    <x-button class="ms-3  bg-green-500 hover:bg-green-600" wire:click="createFeatureSetting" target="createFeatureSetting"
                        :disabled="$form->hasErrors()" icon='cloud-arrow-up'>
                        {{ __('Create') }}
                    </x-button>
                @endif

                <x-action-message class="sm:ms-3 mt-2 sm:mt-0" on="featureSettingCreated">
                    {{ __('Feature setting created successfully.') }}
                </x-action-message>

                <x-action-message class="sm:ms-3 mt-2 sm:mt-0 bg-red-600 text-red-50" on="featureSettingCreationFailed">
                    {{ __('Error occured while trying to create feature setting') }}
                </x-action-message>
            </div>
        </x-slot>
    </x-dialog-modal>
</div>
