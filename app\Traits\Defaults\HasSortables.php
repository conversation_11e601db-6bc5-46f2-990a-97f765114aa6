<?php

namespace App\Traits\Defaults;

use Livewire\Attributes\Url;
use Illuminate\Database\Eloquent\Builder;

trait HasSortables
{
    #[Url]
    public $sortColumn = 'id';
    #[Url]
    public $sortDirection = 'DESC';

    public function sortingDirection(string $column)
    {
        if ($this->sortColumn === $column) {
            $this->sortDirection = ($this->sortDirection === 'DESC') ? 'ASC' : 'DESC';
            return;
        }
        $this->sortColumn = $column;
        $this->sortDirection = 'DESC';
    }

    public function sortBy(Builder $query, $sortColumn, $sortDirection): Builder
    {
        return $query->orderBy($sortColumn, $sortDirection);
    }
}
