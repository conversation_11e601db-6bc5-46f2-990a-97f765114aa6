<?php

namespace App\Services\AllowedMails;

use App\Models\AllowedMail;
use App\Actions\AllowedMails\DeleteAllowedMailAction;
use App\Contracts\AllowedMails\DeleteAllowedMailServiceInterface;

class DeleteAllowedMailService implements DeleteAllowedMailServiceInterface
{
    public function execute(AllowedMail $selectedAllowedMail): bool
    {
        if (!$selectedAllowedMail) {
            return false;
        }
        return DeleteAllowedMailAction::handle($selectedAllowedMail);
    }
}
