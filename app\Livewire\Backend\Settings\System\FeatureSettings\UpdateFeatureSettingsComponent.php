<?php

namespace App\Livewire\Backend\Settings\System\FeatureSettings;

use Livewire\Component;
use App\Models\FeatureSetting;
use App\DTOs\FeatureSettingDTO;
use Livewire\Attributes\Locked;
use Livewire\Attributes\Computed;
use App\Facades\Securities\HoneypotFacade;
use App\Enums\FeatureSettings\FeatureSettingValueEnum;
use App\Livewire\Forms\Securities\RoadAccessForm;
use App\Livewire\Forms\FeatureSettings\FeatureSettingsForm;
use App\Facades\FeatureSettings\UpdateFeatureSettingFacade;
use App\Facades\Securities\OriginHeaderValidatorFacade;

class UpdateFeatureSettingsComponent extends Component
{
    #[Locked]
    public FeatureSetting $featureSetting;

    public FeatureSettingsForm $form;
    public RoadAccessForm $roadAccessForm;

    public $revealUpdateFeatureSettingModal = false;

    public function openModalToUpdateFeatureSetting()
    {
        $this->resetErrorBag();
        $this->form->resetForm();
        $this->roadAccessForm->generateTrackingTime();
        if ($this->roadAccessForm->checkUserAttempts('UpdateFeatureSetting:' . request()->ip(), 6, 300)) {return;}

        $this->form->selectedFeatureSetting = $this->featureSetting;
        $this->form->feature = $this->featureSetting->feature;
        $this->form->value = $this->featureSetting->value->value;
        $this->revealUpdateFeatureSettingModal = true;
    }

    public function updateFeatureSetting()
    {
        $validated = $this->form->validate();
        if ($this->roadAccessForm->checkUserAttempts('UpdateFeatureSetting:' . request()->ip(), 6, 300)) {return;}

        OriginHeaderValidatorFacade::validate();
        HoneypotFacade::inspect($this->roadAccessForm);
        $dto = new FeatureSettingDTO(...$validated);

        try {
            UpdateFeatureSettingFacade::execute($this->featureSetting, $dto);
            $this->dispatch('featureSettingUpdated');
        } catch (\Throwable $th) {
            $this->dispatch('featureSettingUpdateFailed');
        }
    }

    #[Computed]
    public function featureSettingValueOptions()
    {
        return FeatureSettingValueEnum::getFeatureSettingValueOptions();
    }

    public function render()
    {
        return view('livewire.backend.settings.system.feature-settings.update-feature-settings-component');
    }
}
