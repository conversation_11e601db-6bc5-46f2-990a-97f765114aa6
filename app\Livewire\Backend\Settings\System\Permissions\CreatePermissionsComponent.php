<?php

namespace App\Livewire\Backend\Settings\System\Permissions;

use Livewire\Component;
use Livewire\Attributes\Computed;

class CreatePermissionsComponent extends Component
{
    public function createPermission()
    {
        // TODO: Implement permission creation logic
    }

    public function render()
    {
        return view('livewire.backend.settings.system.permissions.create-permissions-component');
    }

    public function placeholder()
    {
        return view('placeholders.lazy-spin-loader');
    }
}
