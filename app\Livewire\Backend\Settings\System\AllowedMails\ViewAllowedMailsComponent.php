<?php

namespace App\Livewire\Backend\Settings\System\AllowedMails;

use Livewire\Component;
use Livewire\Attributes\Locked;
use App\Models\AllowedMail;

class ViewAllowedMailsComponent extends Component
{
    #[Locked]
    public AllowedMail $allowedMail;
    public $revealViewAllowedMailModal = false;

    public function openModalToViewAllowedMail()
    {

        $this->revealViewAllowedMailModal = true;
    }


    public function render()
    {
        return view('livewire.backend.settings.system.allowed-mails.view-allowed-mails-component');
    }
}
