<?php

namespace App\Repositories\AllowedMails;

use Illuminate\Support\Facades\Cache;
use App\Contracts\Repos\AllowedMailsRepos\CountAllowedMailsRepositoryInterface;

class CachedCountAllowedMailsRepository implements CountAllowedMailsRepositoryInterface
{
    public function __construct(private CountAllowedMailsRepositoryInterface $countAllowedMailsRepository) {}

    public function handle(): int
    {
        $key   = 'allowed_mails:count';
        $fresh = 300; // 5 minutes
        $stale = 900; // 15 minutes

        return Cache::flexible($key, [$fresh, $stale], function () {
            return $this->countAllowedMailsRepository->handle();
        });
    }
}
