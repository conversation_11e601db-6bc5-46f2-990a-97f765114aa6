<?php

namespace App\Livewire\Backend\Settings\System\Roles;

use App\DTOs\RoleDTO;
use Livewire\Component;
use Illuminate\Support\Str;
use Livewire\Attributes\Computed;
use App\Enums\Roles\RoleTypesEnum;
use App\Facades\Roles\CreateRoleFacade;
use App\Livewire\Forms\Roles\RolesForm;
use App\Facades\Securities\HoneypotFacade;
use App\Livewire\Forms\Securities\RoadAccessForm;
use App\Facades\Securities\OriginHeaderValidatorFacade;

class CreateRolesComponent extends Component
{
    public RolesForm $form;
    public RoadAccessForm $roadAccessForm;

    public $revealCreateRolesModal = false;

    public function openModalToCreateRole()
    {

        $this->resetErrorBag();
        $this->form->resetForm();
        $this->roadAccessForm->generateTrackingTime();
        if ($this->roadAccessForm->checkUserAttempts('CreateRole:' . request()->ip(), 6, 300)) {return;}
        $this->revealCreateRolesModal = true;
    }

    public function createRole()
    {
        $validated = $this->form->validate();
        if ($this->roadAccessForm->checkUserAttempts('CreateRole:' . request()->ip(), 6, 300)) {return;}

        OriginHeaderValidatorFacade::validate();
        HoneypotFacade::inspect($this->roadAccessForm);
        $validated['slug'] = Str::slug($validated['name']);
        $dto = new RoleDTO(...$validated);

        try {
            CreateRoleFacade::execute($dto);
            $this->dispatch('roleCreated');
            $this->form->resetForm();
        } catch (\Throwable $th) {
            $this->dispatch('roleCreationFailed');
        }
    }

    #[Computed]
    public function roleTypeOptions()
    {
        return RoleTypesEnum::getRoleTypeOptions();
    }



    public function render()
    {
        // $validated = $this->form->validate();
        // dd($this->form->validate(), new RoleDTO(...$validated));
        return view('livewire.backend.settings.system.roles.create-roles-component');
    }
}
