@php
    $tablePagination = $this->allowedMails->isNotEmpty() && $this->totalAllowedMails > 5 ? true : false;
    $allowedMailsAvailable = $tablePagination;
    $rowNumber = 0; // Initialize row number
@endphp
<div>
    <livewire:backend.settings.system.allowed-mails.create-allowed-mails-component />

    {{-- Filters and search --}}
    <div>
        <x-filter :filters="[
            [
                'name' => 'filterMailsByStatus',
                'label' => 'Choose by status',
                'options' => $this->mailStatusOptions,
            ],
        ]" :results="$allowedMailsAvailable" />
    </div>

    {{-- Search --}}
    <x-search :results="$allowedMailsAvailable" searchProperty="allowedMailsSearch"
        searchPlaceholder="Search by mails and descriptions...." formWidth="max-w-full my-3" />

    {{-- Table --}}
    <x-table :columns="[
        ['label' => '#', 'headerClass' => 'border-slate-600'],
        [
            'label' => 'Email',
            'columnName' => 'email',
            'headerClass' => 'border-l border-slate-600 w-32'
        ],
        [
            'label' => 'Status',
            'columnName' => 'status',
            'headerClass' => 'border-l border-slate-600 w-32'
        ],
        ['label' => 'Description', 'headerClass' => 'border-l border-slate-600 line-clamp-1'],
        ['label' => 'Actions', 'headerClass' => 'border-l border-slate-600 w-20'],
    ]" :data="$this->allowedMails" captionClass="text-slate-200" :sort-direction="$sortDirection" :sort-column="$sortColumn"
        :results="$allowedMailsAvailable" maxScrollHeight="max-h-[400px] hidden lg:block pr-1"
        theadClass="bg-slate-700 border-l border-slate-700" subtheadClass="px-2"
        rowClass="border-b hover:bg-blue-700 hover:text-slate-100 hover:border-y-2 hover:border-slate-600 "
        tableClass="border-2 border-slate-700">
        @foreach ($this->allowedMails as $allowedMail)
            <tr wire:key="{{ 'allowedMail-' . $allowedMail->id }}"
                class="border-b bg-slate-800 hover:bg-blue-700 border-slate-700">
                <td class="px-2 py-1  text-slate-200">
                    {{ $loop->iteration }}
                </td>
                <td class="border-l border-slate-600 px-2 py-1 text-slate-200 w-32">
                    {{ $allowedMail->email }}
                </td>
                <td class="border-l border-slate-600 px-2 py-1 text-slate-200 w-32">
                    {{ $allowedMail->status->label() }}
                </td>
                <td class="border-l border-slate-600 px-2 py-1 text-slate-200  line-clamp-1">
                    {{ $allowedMail->description }}
                </td>
                <td class="border-l border-slate-600 px-2 py-1 w-20">
                    <span class="flex justify-end items-center space-x-1">
                        {{-- Action buttons --}}
                        <livewire:backend.settings.system.allowed-mails.view-allowed-mails-component :$allowedMail
                            :key="'view-allowedMail-' . $allowedMail->id">
                            <livewire:backend.settings.system.allowed-mails.update-allowed-mails-component :$allowedMail
                                :key="'update-allowedMail-' . $allowedMail->id">
                                <livewire:backend.settings.system.allowed-mails.delete-allowed-mails-component
                                    :$allowedMail :key="'delete-allowedMail-' . $allowedMail->id">
                    </span>
                </td>
            </tr>
        @endforeach
    </x-table>

    {{-- Table List --}}
    <x-table-list :data="$this->allowedMails" captionMobileClass="text-slate-200" maxScrollHeight="max-h-[500px] lg:hidden pr-1">
        @foreach ($this->allowedMails as $allowedMail)
            @php
                $rowNumber++;
            @endphp
            <div wire:key="{{ 'allowedMail-2-' . $allowedMail->id }}"
                class="p-2 duration-300 ease-in-out rounded-md shadow bg-blue-600 text-slate-50 hover:bg-slate-600 hover:shadow-lg">
                <div class="flex justify-between items-center mb-1 pr-1">
                    <span class="text-sm text-slate-100">Email:</span>
                    <span class="text-sm text-slate-100 line-clamp-1">{{ $allowedMail->email }}</span>
                </div>
                <div class="flex justify-between items-center mb-1 pr-1">
                    <span class="text-sm text-slate-100">Status:</span>
                    <span class="text-sm text-slate-100">{{ $allowedMail->status->label() }}</span>
                </div>
                <div class="mb-1 block bg-slate-800 rounded px-1.5 py-1">
                    <div class=" text-amber-200">Description:</div>
                    <div class="text-sm text-slate-200 line-clamp-2">{{ $allowedMail->description }}</div>
                </div>
                <div class="flex justify-between items-center">
                    <span class="flex justify-end items-center space-x-1">
                        {{-- Action buttons --}}
                        <livewire:backend.settings.system.allowed-mails.view-allowed-mails-component :$allowedMail
                            :key="'view-2-allowedMail-' . $allowedMail->id">
                            <livewire:backend.settings.system.allowed-mails.update-allowed-mails-component :$allowedMail
                                :key="'update-2-allowedMail-' . $allowedMail->id">
                                <livewire:backend.settings.system.allowed-mails.delete-allowed-mails-component
                                    :$allowedMail :key="'delete-2-allowedMail-' . $allowedMail->id">
                    </span>
                    @if ($rowNumber >= 1)
                        <span
                            class="text-right rounded-full px-2 bg-slate-950 text-slate-50 text-xs">{{ $rowNumber }}</span>
                    @endif
                </div>
            </div>
        @endforeach
    </x-table-list>

    {{-- Pagination --}}
    <x-paginator :pages="$this->allowedMailsPages" :totalRecords="$this->totalAllowedMails" selectClass="text-slate-500"
        paginateRecords="allowedMailsPaginate" :results="$allowedMailsAvailable" :paginationEnabled="$tablePagination" />
</div>
