<?php

namespace App\Services\AllowedMails;

use App\Models\AllowedMail;
use App\DTOs\AllowedMailDTO;
use App\Actions\AllowedMails\UpdateAllowedMailAction;
use App\Contracts\AllowedMails\UpdateAllowedMailServiceInterface;

class UpdateAllowedMailService implements UpdateAllowedMailServiceInterface
{
    public function execute(AllowedMail $selectedAllowedMail, AllowedMailDTO $dto): AllowedMail
    {
        return UpdateAllowedMailAction::handle($selectedAllowedMail, $dto->toArray());
    }
}
