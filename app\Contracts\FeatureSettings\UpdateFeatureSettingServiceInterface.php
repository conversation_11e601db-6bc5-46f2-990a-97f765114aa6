<?php

namespace App\Contracts\FeatureSettings;

use App\Models\FeatureSetting;
use App\DTOs\FeatureSettingDTO;
use App\Services\FeatureSettings\UpdateFeatureSettingService;
use Illuminate\Container\Attributes\Bind;

#[Bind(UpdateFeatureSettingService::class)]
interface UpdateFeatureSettingServiceInterface
{
    public function execute(FeatureSetting $selectedFeatureSetting, FeatureSettingDTO $dto): FeatureSetting;
}
