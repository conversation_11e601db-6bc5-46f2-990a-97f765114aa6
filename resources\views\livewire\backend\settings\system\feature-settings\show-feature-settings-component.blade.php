@php
    $tablePagination = $this->featureSettings->isNotEmpty() && $this->totalFeatureSettings > 5 ? true : false;
    $featureSettingsAvailable = $tablePagination;
    $rowNumber = 0; // Initialize row number
@endphp
<div>

    <livewire:backend.settings.system.feature-settings.create-feature-settings-component />

    {{-- Filters and search --}}
    <div>
        <x-filter :filters="[
            [
                'name' => 'filterFeatureSettingsByValue',
                'label' => 'Choose by value',
                'options' => $this->featureSettingValueOptions,
            ],
        ]" :results="$featureSettingsAvailable" />
    </div>

    {{-- Search --}}
    <x-search :results="$featureSettingsAvailable" searchProperty="featureSettingsSearch" searchPlaceholder="Search by feature name...."
        formWidth="max-w-full my-3" />

    {{-- Table --}}
    <x-table :columns="[
        [
            'label' => '#',
            'headerClass' => 'border-slate-600 w-24',
        ],
        [
            'label' => 'Feature name',
            'columnName' => 'feature', // <-- for the arrow
            'headerClass' => 'border-l border-slate-600 w-48',
        ],
        [
            'label' => 'Value',
            'columnName' => 'value',
            'headerClass' => 'border-l border-slate-600 w-32',
        ],
        [
            'label' => 'Actions',
            'headerClass' => 'border-l border-slate-600 w-1/6',
        ],
    ]" :data="$this->featureSettings" captionClass="text-slate-200"
        :sort-direction="$sortDirection" :sort-column="$sortColumn" :results="$featureSettingsAvailable"
        maxScrollHeight="max-h-[500px] hidden lg:block pr-1" theadClass="bg-slate-700 border-l border-slate-700"
        subtheadClass="px-2"
        rowClass="border-b hover:bg-slate-700 hover:text-slate-100 hover:border-y-2 hover:border-slate-600 "
        tableClass="border-2 border-slate-700">

        @foreach ($this->featureSettings as $featureSetting)
            <tr wire:key="{{ 'feature-setting-' . $featureSetting->id }}"
                class="border-b bg-slate-800 hover:bg-slate-700 border-slate-700">
                <td class="px-2 py-1  text-slate-200 w-24">
                    {{ $loop->iteration }}
                </td>
                <td class="border-l border-slate-600 px-2 py-1 text-slate-200 w-48">
                    {{ $featureSetting->feature }}
                </td>
                <td class="border-l border-slate-600 px-2 py-1 text-slate-200 w-32">
                    <x-boolean-badge value="{{ $featureSetting->value->value === 2 ? true : false }}"
                        trueStatement="Enabled" falseStatement="Disabled" />
                </td>
                <td class="border-l border-slate-600 px-2 py-1 w-1/6">
                    <span class="flex items-center space-x-1">
                        {{-- Action buttons --}}
                        <livewire:backend.settings.system.feature-settings.view-feature-settings-component
                            :$featureSetting :key="'view-feature-setting-' . $featureSetting->id">
                            <livewire:backend.settings.system.feature-settings.update-feature-settings-component
                                :$featureSetting :key="'update-feature-setting-' . $featureSetting->id">
                                <livewire:backend.settings.system.feature-settings.delete-feature-settings-component
                                    :$featureSetting :key="'delete-feature-setting-' . $featureSetting->id">
                    </span>

                </td>
            </tr>
        @endforeach
    </x-table>

    {{-- Table List --}}
    <x-table-list :data="$this->featureSettings" captionMobileClass="text-slate-200" maxScrollHeight="max-h-[500px] lg:hidden pr-1">
        @foreach ($this->featureSettings as $featureSetting)
            @php
                $rowNumber++;
            @endphp
            <div wire:key="{{ 'feature-setting-2-' . $featureSetting->id }}"
                class="p-2 duration-300 ease-in-out rounded-md shadow bg-slate-600 text-slate-50 hover:bg-slate-700 hover:shadow-lg">
                <div class="flex justify-between items-center mb-1 pr-1">
                    <span class="text-sm text-slate-100">Feature:</span>
                    <span class="text-sm text-slate-100">{{ $featureSetting->feature }}</span>
                </div>
                <div class="flex justify-between items-center mb-1 pr-1">
                    <span class="text-sm text-slate-100">Value:</span>
                    <x-boolean-badge value="{{ $featureSetting->value->value === 2 ? true : false }}"
                        trueStatement="Enabled" falseStatement="Disabled" />
                </div>
                <div class="flex justify-end items-center space-x-1 mt-2">
                    <livewire:backend.settings.system.feature-settings.view-feature-settings-component :$featureSetting
                        :key="'view-2-feature-setting-' . $featureSetting->id">
                        <livewire:backend.settings.system.feature-settings.update-feature-settings-component
                            :$featureSetting :key="'update-2-feature-setting-' . $featureSetting->id">
                            <livewire:backend.settings.system.feature-settings.delete-feature-settings-component
                                :$featureSetting :key="'delete-2-feature-setting-' . $featureSetting->id">
                </div>
            </div>
        @endforeach
    </x-table-list>

    {{-- Pagination --}}
    <x-paginator  :pages="$this->featureSettingsPages" :totalRecords="$this->totalFeatureSettings" selectClass="text-slate-500"
        paginateRecords="featureSettingsPaginate" :results="$featureSettingsAvailable" :paginationEnabled="$tablePagination" />




</div>
