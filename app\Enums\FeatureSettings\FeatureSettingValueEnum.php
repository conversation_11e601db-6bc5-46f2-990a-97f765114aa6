<?php

namespace App\Enums\FeatureSettings;

enum FeatureSettingValueEnum: int
{
    case DISABLED = 1;
    case ENABLED = 2;

    public function label(): string
    {
        return match ($this) {
            self::DISABLED => 'Disabled',
            self::ENABLED => 'Enabled',
        };
    }

    public static function getFeatureSettingValueOptions(): array
    {
        $options = [];
        foreach (self::cases() as $case) {
            $options[$case->value] = $case->label();
        }
        return $options;
    }
}
