<?php

namespace App\Livewire\Backend\Settings\System\AllowedMails;

use Livewire\Component;
use App\DTOs\AllowedMailDTO;
use Livewire\Attributes\Computed;
use App\Facades\Securities\HoneypotFacade;
use App\Enums\AllowedMails\AllowedMailStatusEnum;
use App\Livewire\Forms\Securities\RoadAccessForm;
use App\Livewire\Forms\AllowedMails\AllowedMailsForm;
use App\Facades\AllowedMails\CreateAllowedMailFacade;
use App\Facades\Securities\OriginHeaderValidatorFacade;

class CreateAllowedMailsComponent extends Component
{
    public AllowedMailsForm $form;
    public RoadAccessForm $roadAccessForm;
    public $revealCreateAllowedMailModal = false;

    public function openModalToCreateAllowedMail()
    {
        $this->resetErrorBag();
        $this->form->resetForm();
        $this->roadAccessForm->generateTrackingTime();
        if ($this->roadAccessForm->checkUserAttempts('CreateAllowedMail:' . request()->ip(), 6, 300)) {return;}
        $this->revealCreateAllowedMailModal = true;
    }
    public function createAllowedMail()
    {
        $validated = $this->form->validate();
        if ($this->roadAccessForm->checkUserAttempts('CreateAllowedMail:' . request()->ip(), 6, 300)) {return;}

        OriginHeaderValidatorFacade::validate();
        HoneypotFacade::inspect($this->roadAccessForm);
        $dto = new AllowedMailDTO(...$validated);

        try {
            CreateAllowedMailFacade::execute($dto);
            $this->form->resetForm();
            $this->dispatch('allowedMailCreated');
        } catch (\Throwable $th) {
            $this->dispatch('allowedMailCreationFailed');
        }
    }

    #[Computed]
    public function mailStatusOptions()
    {
        return AllowedMailStatusEnum::getMailStatusOptions();
    }


    public function render()
    {
        return view('livewire.backend.settings.system.allowed-mails.create-allowed-mails-component');
    }
}
