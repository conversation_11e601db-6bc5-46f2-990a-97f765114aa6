<div>
    <?php if (isset($component)) { $__componentOriginalf9332b595ad3d3a806f9da4dda8769dd = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf9332b595ad3d3a806f9da4dda8769dd = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.action-buttons','data' => ['id' => $blockedIp->id,'editAction' => 'openModalToUpdateBlockedIp']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('action-buttons'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['id' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($blockedIp->id),'editAction' => 'openModalToUpdateBlockedIp']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf9332b595ad3d3a806f9da4dda8769dd)): ?>
<?php $attributes = $__attributesOriginalf9332b595ad3d3a806f9da4dda8769dd; ?>
<?php unset($__attributesOriginalf9332b595ad3d3a806f9da4dda8769dd); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf9332b595ad3d3a806f9da4dda8769dd)): ?>
<?php $component = $__componentOriginalf9332b595ad3d3a806f9da4dda8769dd; ?>
<?php unset($__componentOriginalf9332b595ad3d3a806f9da4dda8769dd); ?>
<?php endif; ?>

    <?php if (isset($component)) { $__componentOriginal49bd1c1dd878e22e0fb84faabf295a3f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal49bd1c1dd878e22e0fb84faabf295a3f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dialog-modal','data' => ['wire:model' => 'revealUpdateBlockedIpModal','maxWidth' => 'xl']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dialog-modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['wire:model' => 'revealUpdateBlockedIpModal','maxWidth' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute('xl')]); ?>
         <?php $__env->slot('content', null, []); ?> 
            <?php if (isset($component)) { $__componentOriginal73a6b0261bc3d280b80775663eef6108 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal73a6b0261bc3d280b80775663eef6108 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.divider','data' => ['nameClass' => 'text-blue-400']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('divider'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['nameClass' => 'text-blue-400']); ?>
                <?php echo e(__('UPDATE BLOCKED IP ADDRESS')); ?>

             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal73a6b0261bc3d280b80775663eef6108)): ?>
<?php $attributes = $__attributesOriginal73a6b0261bc3d280b80775663eef6108; ?>
<?php unset($__attributesOriginal73a6b0261bc3d280b80775663eef6108); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal73a6b0261bc3d280b80775663eef6108)): ?>
<?php $component = $__componentOriginal73a6b0261bc3d280b80775663eef6108; ?>
<?php unset($__componentOriginal73a6b0261bc3d280b80775663eef6108); ?>
<?php endif; ?>
         <?php $__env->endSlot(); ?>
         <?php $__env->slot('content', null, []); ?> 
            <?php if (isset($component)) { $__componentOriginal7f532b4338f78b38e13c5b83e10dff77 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7f532b4338f78b38e13c5b83e10dff77 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.road-access','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('road-access'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7f532b4338f78b38e13c5b83e10dff77)): ?>
<?php $attributes = $__attributesOriginal7f532b4338f78b38e13c5b83e10dff77; ?>
<?php unset($__attributesOriginal7f532b4338f78b38e13c5b83e10dff77); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7f532b4338f78b38e13c5b83e10dff77)): ?>
<?php $component = $__componentOriginal7f532b4338f78b38e13c5b83e10dff77; ?>
<?php unset($__componentOriginal7f532b4338f78b38e13c5b83e10dff77); ?>
<?php endif; ?>
            <?php if (isset($component)) { $__componentOriginal834204310c6424a466154465717204ee = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal834204310c6424a466154465717204ee = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.rate-limiter-message','data' => ['message' => $this->roadAccessForm->road_access_rate_limiter]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('rate-limiter-message'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['message' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($this->roadAccessForm->road_access_rate_limiter)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal834204310c6424a466154465717204ee)): ?>
<?php $attributes = $__attributesOriginal834204310c6424a466154465717204ee; ?>
<?php unset($__attributesOriginal834204310c6424a466154465717204ee); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal834204310c6424a466154465717204ee)): ?>
<?php $component = $__componentOriginal834204310c6424a466154465717204ee; ?>
<?php unset($__componentOriginal834204310c6424a466154465717204ee); ?>
<?php endif; ?>

            <!--[if BLOCK]><![endif]--><?php if(!$this->roadAccessForm->road_access_rate_limiter): ?>
                <?php if (isset($component)) { $__componentOriginalc1874febf49a28dae7622f110fa0eeaf = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc1874febf49a28dae7622f110fa0eeaf = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.lable-input-error','data' => ['id' => 'form.ip_address','type' => 'text','label' => 'IP address','model' => 'form.ip_address','inputType' => 'input','value' => ''.e($form->ip_address).'','onEnter' => $form->getUpdateBlockedIpEvent(),'placeholder' => 'Enter blocked IP address....','modelModifier' => 'live.debounce.400ms','inputClass' => 'w-full','icon' => 'globe','iconClass' => 'ps-2.5']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('lable-input-error'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['id' => 'form.ip_address','type' => 'text','label' => 'IP address','model' => 'form.ip_address','inputType' => 'input','value' => ''.e($form->ip_address).'','onEnter' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($form->getUpdateBlockedIpEvent()),'placeholder' => 'Enter blocked IP address....','modelModifier' => 'live.debounce.400ms','inputClass' => 'w-full','icon' => 'globe','iconClass' => 'ps-2.5']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc1874febf49a28dae7622f110fa0eeaf)): ?>
<?php $attributes = $__attributesOriginalc1874febf49a28dae7622f110fa0eeaf; ?>
<?php unset($__attributesOriginalc1874febf49a28dae7622f110fa0eeaf); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc1874febf49a28dae7622f110fa0eeaf)): ?>
<?php $component = $__componentOriginalc1874febf49a28dae7622f110fa0eeaf; ?>
<?php unset($__componentOriginalc1874febf49a28dae7622f110fa0eeaf); ?>
<?php endif; ?>

                <?php if (isset($component)) { $__componentOriginalc1874febf49a28dae7622f110fa0eeaf = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc1874febf49a28dae7622f110fa0eeaf = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.lable-input-error','data' => ['id' => 'form.reason','type' => 'text','label' => 'Reason','model' => 'form.reason','inputType' => 'textarea','value' => ''.e($form->reason).'','onEnter' => $form->getUpdateBlockedIpEvent(),'placeholder' => 'Enter reason for blocking this IP....','modelModifier' => 'live.debounce.500ms','inputClass' => 'w-full','parentClass' => 'mt-3']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('lable-input-error'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['id' => 'form.reason','type' => 'text','label' => 'Reason','model' => 'form.reason','inputType' => 'textarea','value' => ''.e($form->reason).'','onEnter' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($form->getUpdateBlockedIpEvent()),'placeholder' => 'Enter reason for blocking this IP....','modelModifier' => 'live.debounce.500ms','inputClass' => 'w-full','parentClass' => 'mt-3']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc1874febf49a28dae7622f110fa0eeaf)): ?>
<?php $attributes = $__attributesOriginalc1874febf49a28dae7622f110fa0eeaf; ?>
<?php unset($__attributesOriginalc1874febf49a28dae7622f110fa0eeaf); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc1874febf49a28dae7622f110fa0eeaf)): ?>
<?php $component = $__componentOriginalc1874febf49a28dae7622f110fa0eeaf; ?>
<?php unset($__componentOriginalc1874febf49a28dae7622f110fa0eeaf); ?>
<?php endif; ?>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

            <div class="sm:flex items-center mt-4">
                <?php if (isset($component)) { $__componentOriginald0f1fd2689e4bb7060122a5b91fe8561 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.button','data' => ['class' => ' bg-red-500 hover:bg-red-600','wire:click' => '$toggle(\'revealUpdateBlockedIpModal\')','target' => 'revealUpdateBlockedIpModal','icon' => 'ban']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => ' bg-red-500 hover:bg-red-600','wire:click' => '$toggle(\'revealUpdateBlockedIpModal\')','target' => 'revealUpdateBlockedIpModal','icon' => 'ban']); ?>
                    <?php echo e(__('Cancel')); ?>

                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561)): ?>
<?php $attributes = $__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561; ?>
<?php unset($__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald0f1fd2689e4bb7060122a5b91fe8561)): ?>
<?php $component = $__componentOriginald0f1fd2689e4bb7060122a5b91fe8561; ?>
<?php unset($__componentOriginald0f1fd2689e4bb7060122a5b91fe8561); ?>
<?php endif; ?>

                <!--[if BLOCK]><![endif]--><?php if(!$this->roadAccessForm->road_access_rate_limiter): ?>
                    <?php if (isset($component)) { $__componentOriginald0f1fd2689e4bb7060122a5b91fe8561 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.button','data' => ['class' => 'ms-3  bg-green-500 hover:bg-green-600','wire:click' => 'updateBlockedIp','target' => 'updateBlockedIp','disabled' => $form->hasErrors(),'icon' => 'cloud-arrow-up']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'ms-3  bg-green-500 hover:bg-green-600','wire:click' => 'updateBlockedIp','target' => 'updateBlockedIp','disabled' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($form->hasErrors()),'icon' => 'cloud-arrow-up']); ?>
                        <?php echo e(__('Update')); ?>

                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561)): ?>
<?php $attributes = $__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561; ?>
<?php unset($__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald0f1fd2689e4bb7060122a5b91fe8561)): ?>
<?php $component = $__componentOriginald0f1fd2689e4bb7060122a5b91fe8561; ?>
<?php unset($__componentOriginald0f1fd2689e4bb7060122a5b91fe8561); ?>
<?php endif; ?>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                <?php if (isset($component)) { $__componentOriginala665a74688c74e9ee80d4fedd2b98434 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginala665a74688c74e9ee80d4fedd2b98434 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.action-message','data' => ['class' => 'sm:ms-3 mt-2 sm:mt-0','on' => 'blockedIpUpdated']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('action-message'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'sm:ms-3 mt-2 sm:mt-0','on' => 'blockedIpUpdated']); ?>
                    <?php echo e(__('Blocked IP address updated successfully.')); ?>

                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginala665a74688c74e9ee80d4fedd2b98434)): ?>
<?php $attributes = $__attributesOriginala665a74688c74e9ee80d4fedd2b98434; ?>
<?php unset($__attributesOriginala665a74688c74e9ee80d4fedd2b98434); ?>
<?php endif; ?>
<?php if (isset($__componentOriginala665a74688c74e9ee80d4fedd2b98434)): ?>
<?php $component = $__componentOriginala665a74688c74e9ee80d4fedd2b98434; ?>
<?php unset($__componentOriginala665a74688c74e9ee80d4fedd2b98434); ?>
<?php endif; ?>

                <?php if (isset($component)) { $__componentOriginala665a74688c74e9ee80d4fedd2b98434 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginala665a74688c74e9ee80d4fedd2b98434 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.action-message','data' => ['class' => 'sm:ms-3 mt-2 sm:mt-0 bg-red-600 text-red-50','on' => 'blockedIpUpdateFailed']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('action-message'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'sm:ms-3 mt-2 sm:mt-0 bg-red-600 text-red-50','on' => 'blockedIpUpdateFailed']); ?>
                    <?php echo e(__('Error occured while trying to update blocked IP address')); ?>

                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginala665a74688c74e9ee80d4fedd2b98434)): ?>
<?php $attributes = $__attributesOriginala665a74688c74e9ee80d4fedd2b98434; ?>
<?php unset($__attributesOriginala665a74688c74e9ee80d4fedd2b98434); ?>
<?php endif; ?>
<?php if (isset($__componentOriginala665a74688c74e9ee80d4fedd2b98434)): ?>
<?php $component = $__componentOriginala665a74688c74e9ee80d4fedd2b98434; ?>
<?php unset($__componentOriginala665a74688c74e9ee80d4fedd2b98434); ?>
<?php endif; ?>
            </div>
         <?php $__env->endSlot(); ?>

     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal49bd1c1dd878e22e0fb84faabf295a3f)): ?>
<?php $attributes = $__attributesOriginal49bd1c1dd878e22e0fb84faabf295a3f; ?>
<?php unset($__attributesOriginal49bd1c1dd878e22e0fb84faabf295a3f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal49bd1c1dd878e22e0fb84faabf295a3f)): ?>
<?php $component = $__componentOriginal49bd1c1dd878e22e0fb84faabf295a3f; ?>
<?php unset($__componentOriginal49bd1c1dd878e22e0fb84faabf295a3f); ?>
<?php endif; ?>

</div>
<?php /**PATH C:\Users\<USER>\Herd\laraauthstarter\resources\views/livewire/backend/settings/system/blocked-ips/update-blocked-ips-component.blade.php ENDPATH**/ ?>