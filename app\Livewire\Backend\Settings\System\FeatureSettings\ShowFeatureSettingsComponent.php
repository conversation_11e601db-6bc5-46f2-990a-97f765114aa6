<?php

namespace App\Livewire\Backend\Settings\System\FeatureSettings;

use Livewire\Component;
use Livewire\Attributes\On;
use Livewire\Attributes\Url;
use Livewire\WithPagination;
use App\Models\FeatureSetting;
use Livewire\Attributes\Computed;
use App\Supports\PaginatorSupport;
use App\Traits\Defaults\HasSortables;
use App\Enums\FeatureSettings\FeatureSettingValueEnum;
use App\Facades\Repos\FeatureSettings\CountFeatureSettingsFacade;


class ShowFeatureSettingsComponent extends Component
{
    use WithPagination;
    use HasSortables;

    #[Url]
    public $featureSettingsSearch = '';
    #[Url]
    public $featureSettingsPaginate = 5;
    #[Url]
    public $filterFeatureSettingsByValue = '';

    public function doSort(string $column)
    {
        $this->sortingDirection($column);
    }


    #[Computed]
    public function featureSettings()
    {
        $selectedColumns = ['id', 'feature', 'value'];
        $searchTerm = trim('%' . $this->featureSettingsSearch . '%');
        $filterTerms = [
            'filterFeatureSettingByValue' => $this->filterFeatureSettingsByValue,
        ];

        $query = FeatureSetting::query();
        $query = $query->select($selectedColumns);
        $query = $query->search($searchTerm);
        $query = $query->filter($filterTerms);
        $query = $this->sortBy($query, $this->sortColumn, $this->sortDirection);
        $query = $query->paginate($this->featureSettingsPaginate);

        return $query;
    }

    #[Computed]
    public function featureSettingValueOptions()
    {
        return FeatureSettingValueEnum::getFeatureSettingValueOptions();
    }

    #[Computed]
    public function totalFeatureSettings()
    {
        return CountFeatureSettingsFacade::handle();
    }

    #[Computed()]
    public function featureSettingsPages(): array
    {
        return PaginatorSupport::generatePages($this->totalFeatureSettings);
    }

    #[On(['featureSettingCreated', 'featureSettingUpdated', 'featureSettingDeleted'])]
    public function render()
    {
        return view('livewire.backend.settings.system.feature-settings.show-feature-settings-component');
    }

    public function placeholder()
    {
        return view('placeholders.lazy-spin-loader');
    }
}
