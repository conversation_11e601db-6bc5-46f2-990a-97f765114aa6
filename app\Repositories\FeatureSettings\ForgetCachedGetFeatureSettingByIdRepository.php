<?php

namespace App\Repositories\FeatureSettings;

use Illuminate\Support\Facades\Cache;
use App\Contracts\Repos\FeatureSettingsRepos\ForgetCachedGetFeatureSettingByIdRepositoryInterface;

final class ForgetCachedGetFeatureSettingByIdRepository implements ForgetCachedGetFeatureSettingByIdRepositoryInterface
{
    public function handle(int $id, array $selectedColumns = ['*']): void
    {
        $columns = $selectedColumns;
        sort($columns); // normalize to avoid order mismatch
        $key = 'feature_setting_id:' . $id . ':' . implode(',', $columns);
        Cache::forget($key);
    }
}
