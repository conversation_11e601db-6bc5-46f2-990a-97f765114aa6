<?php

namespace App\Livewire\Forms\Roles;

use Livewire\Form;
use App\Models\Role;
use App\Rules\GibberishRule;
use Illuminate\Validation\Rule;
use App\Enums\Roles\RoleTypesEnum;
use Livewire\Attributes\Validate;


class RolesForm extends Form
{
    #[Validate]
    public $name;
    #[Validate]
    public $description;
    #[Validate]
    public $type;

    public ?Role $selectedRole  = null;


    public function rules(): array
    {
        return [
            'name' => array_filter([
                'required',
                'string',
                'min:3',
                'max:55',
                new GibberishRule('name', 3, 2),
                Rule::unique('roles')->ignore($this->selectedRole)
            ]),
            'type' => [
                'required',
                Rule::in(array_column(RoleTypesEnum::cases(), 'value'))
            ],

            'description' => array_filter([
                'required',
                'string',
                'min:5',
                'max:500',
                new GibberishRule('pattern', 3, 2)
            ]),
        ];
    }

    public function resetForm()
    {
        $this->reset(['name', 'type', 'description']);
    }

    public function hasErrors(): bool
    {
        return $this->getErrorBag()->isNotEmpty() || empty($this->name) || empty($this->name)  || empty($this->description);
    }

    public function getCreateRoleEvent(): string
    {
        return $this->hasErrors() ? '' : 'createRole';
    }

    public function getUpdateRoleEvent(): string
    {
        return $this->hasErrors() ? '' : 'updateRole';
    }
}
