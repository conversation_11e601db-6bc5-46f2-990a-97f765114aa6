<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Database\Seeders\RoleSeeder;
use Database\Seeders\BlockedIpSeeder;
use Database\Seeders\UntrustedHeaderSeeder;
use Database\Seeders\AllowedExternalIpSeeder;
use Database\Seeders\AllowedMailSeeder;
use Database\Seeders\FeatureSettingSeeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call([
            RoleSeeder::class,
            UntrustedHeaderSeeder::class,
            BlockedIpSeeder::class,
            AllowedExternalIpSeeder::class,
            AllowedMailSeeder::class,
            FeatureSettingSeeder::class,
        ]);
    }
}
