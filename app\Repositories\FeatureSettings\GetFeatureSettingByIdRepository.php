<?php

namespace App\Repositories\FeatureSettings;

use App\Models\FeatureSetting;
use App\Contracts\Repos\FeatureSettingsRepos\GetFeatureSettingByIdRepositoryInterface;

final class GetFeatureSettingByIdRepository implements GetFeatureSettingByIdRepositoryInterface
{
    public function handle(int $id, array $selectedColumns = ['*']): ?FeatureSetting
    {
        return FeatureSetting::select($selectedColumns)->find($id);
    }
}
