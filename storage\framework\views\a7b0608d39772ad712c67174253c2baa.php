<div>
    <?php if (isset($component)) { $__componentOriginald0f1fd2689e4bb7060122a5b91fe8561 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.button','data' => ['class' => 'bg-slate-600 hover:bg-green-600','wire:click' => 'openModalToCreateUntrustedHeader','target' => 'openModalToCreateUntrustedHeader','icon' => 'circle-plus']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'bg-slate-600 hover:bg-green-600','wire:click' => 'openModalToCreateUntrustedHeader','target' => 'openModalToCreateUntrustedHeader','icon' => 'circle-plus']); ?>
        <?php echo e(__('Create Header')); ?>

     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561)): ?>
<?php $attributes = $__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561; ?>
<?php unset($__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald0f1fd2689e4bb7060122a5b91fe8561)): ?>
<?php $component = $__componentOriginald0f1fd2689e4bb7060122a5b91fe8561; ?>
<?php unset($__componentOriginald0f1fd2689e4bb7060122a5b91fe8561); ?>
<?php endif; ?>

    <?php if (isset($component)) { $__componentOriginal49bd1c1dd878e22e0fb84faabf295a3f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal49bd1c1dd878e22e0fb84faabf295a3f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dialog-modal','data' => ['wire:model' => 'revealCreateUntrustedHeaderModal','maxWidth' => 'xl']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dialog-modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['wire:model' => 'revealCreateUntrustedHeaderModal','maxWidth' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute('xl')]); ?>
         <?php $__env->slot('content', null, []); ?> 
            <?php if (isset($component)) { $__componentOriginal73a6b0261bc3d280b80775663eef6108 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal73a6b0261bc3d280b80775663eef6108 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.divider','data' => ['nameClass' => 'text-blue-400']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('divider'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['nameClass' => 'text-blue-400']); ?>
                <?php echo e(__('CREATE UNTRUSTED HEADER')); ?>

             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal73a6b0261bc3d280b80775663eef6108)): ?>
<?php $attributes = $__attributesOriginal73a6b0261bc3d280b80775663eef6108; ?>
<?php unset($__attributesOriginal73a6b0261bc3d280b80775663eef6108); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal73a6b0261bc3d280b80775663eef6108)): ?>
<?php $component = $__componentOriginal73a6b0261bc3d280b80775663eef6108; ?>
<?php unset($__componentOriginal73a6b0261bc3d280b80775663eef6108); ?>
<?php endif; ?>

            <?php if (isset($component)) { $__componentOriginal7f532b4338f78b38e13c5b83e10dff77 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7f532b4338f78b38e13c5b83e10dff77 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.road-access','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('road-access'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7f532b4338f78b38e13c5b83e10dff77)): ?>
<?php $attributes = $__attributesOriginal7f532b4338f78b38e13c5b83e10dff77; ?>
<?php unset($__attributesOriginal7f532b4338f78b38e13c5b83e10dff77); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7f532b4338f78b38e13c5b83e10dff77)): ?>
<?php $component = $__componentOriginal7f532b4338f78b38e13c5b83e10dff77; ?>
<?php unset($__componentOriginal7f532b4338f78b38e13c5b83e10dff77); ?>
<?php endif; ?>
            <?php if (isset($component)) { $__componentOriginal834204310c6424a466154465717204ee = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal834204310c6424a466154465717204ee = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.rate-limiter-message','data' => ['message' => $this->roadAccessForm->road_access_rate_limiter]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('rate-limiter-message'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['message' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($this->roadAccessForm->road_access_rate_limiter)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal834204310c6424a466154465717204ee)): ?>
<?php $attributes = $__attributesOriginal834204310c6424a466154465717204ee; ?>
<?php unset($__attributesOriginal834204310c6424a466154465717204ee); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal834204310c6424a466154465717204ee)): ?>
<?php $component = $__componentOriginal834204310c6424a466154465717204ee; ?>
<?php unset($__componentOriginal834204310c6424a466154465717204ee); ?>
<?php endif; ?>

            <!--[if BLOCK]><![endif]--><?php if(!$this->roadAccessForm->road_access_rate_limiter): ?>
                <?php if (isset($component)) { $__componentOriginal655b2d8713356738418255a5c8b1912b = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal655b2d8713356738418255a5c8b1912b = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.lable-select-error','data' => ['parentClass' => 'mt-2','labelClass' => 'w-full text-slate-300','selectClass' => 'font-bold text-green-800','id' => 'form.header_type','label' => 'Header name','model' => 'form.header_type','placeholder' => 'Select header name','options' => $this->headerNamesOptions,'value' => $form->header_type,'modelModifier' => 'live.debounce.500ms']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('lable-select-error'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['parentClass' => 'mt-2','labelClass' => 'w-full text-slate-300','selectClass' => 'font-bold text-green-800','id' => 'form.header_type','label' => 'Header name','model' => 'form.header_type','placeholder' => 'Select header name','options' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($this->headerNamesOptions),'value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($form->header_type),'modelModifier' => 'live.debounce.500ms']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal655b2d8713356738418255a5c8b1912b)): ?>
<?php $attributes = $__attributesOriginal655b2d8713356738418255a5c8b1912b; ?>
<?php unset($__attributesOriginal655b2d8713356738418255a5c8b1912b); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal655b2d8713356738418255a5c8b1912b)): ?>
<?php $component = $__componentOriginal655b2d8713356738418255a5c8b1912b; ?>
<?php unset($__componentOriginal655b2d8713356738418255a5c8b1912b); ?>
<?php endif; ?>

                <?php if (isset($component)) { $__componentOriginalc1874febf49a28dae7622f110fa0eeaf = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc1874febf49a28dae7622f110fa0eeaf = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.lable-input-error','data' => ['id' => 'form.pattern','type' => 'text','label' => 'Pattern','model' => 'form.pattern','inputType' => 'input','value' => ''.e($form->pattern).'','onEnter' => $form->getCreateUntrustedHeaderEvent(),'placeholder' => 'Enter header pattern....','modelModifier' => 'live.debounce.400ms','inputClass' => 'w-full','icon' => 'paw','iconClass' => 'ps-2.5','parentClass' => 'mt-1.5']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('lable-input-error'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['id' => 'form.pattern','type' => 'text','label' => 'Pattern','model' => 'form.pattern','inputType' => 'input','value' => ''.e($form->pattern).'','onEnter' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($form->getCreateUntrustedHeaderEvent()),'placeholder' => 'Enter header pattern....','modelModifier' => 'live.debounce.400ms','inputClass' => 'w-full','icon' => 'paw','iconClass' => 'ps-2.5','parentClass' => 'mt-1.5']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc1874febf49a28dae7622f110fa0eeaf)): ?>
<?php $attributes = $__attributesOriginalc1874febf49a28dae7622f110fa0eeaf; ?>
<?php unset($__attributesOriginalc1874febf49a28dae7622f110fa0eeaf); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc1874febf49a28dae7622f110fa0eeaf)): ?>
<?php $component = $__componentOriginalc1874febf49a28dae7622f110fa0eeaf; ?>
<?php unset($__componentOriginalc1874febf49a28dae7622f110fa0eeaf); ?>
<?php endif; ?>

                <?php if (isset($component)) { $__componentOriginalc1874febf49a28dae7622f110fa0eeaf = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalc1874febf49a28dae7622f110fa0eeaf = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.lable-input-error','data' => ['id' => 'form.description','type' => 'text','label' => 'Role description','model' => 'form.description','inputType' => 'textarea','value' => ''.e($form->description).'','onEnter' => $form->getCreateUntrustedHeaderEvent(),'placeholder' => 'Enter role description....','modelModifier' => 'live.debounce.500ms','inputClass' => 'w-full','parentClass' => 'mt-3']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('lable-input-error'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['id' => 'form.description','type' => 'text','label' => 'Role description','model' => 'form.description','inputType' => 'textarea','value' => ''.e($form->description).'','onEnter' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($form->getCreateUntrustedHeaderEvent()),'placeholder' => 'Enter role description....','modelModifier' => 'live.debounce.500ms','inputClass' => 'w-full','parentClass' => 'mt-3']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalc1874febf49a28dae7622f110fa0eeaf)): ?>
<?php $attributes = $__attributesOriginalc1874febf49a28dae7622f110fa0eeaf; ?>
<?php unset($__attributesOriginalc1874febf49a28dae7622f110fa0eeaf); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc1874febf49a28dae7622f110fa0eeaf)): ?>
<?php $component = $__componentOriginalc1874febf49a28dae7622f110fa0eeaf; ?>
<?php unset($__componentOriginalc1874febf49a28dae7622f110fa0eeaf); ?>
<?php endif; ?>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

            <div class="sm:flex items-center mt-4">
                <?php if (isset($component)) { $__componentOriginald0f1fd2689e4bb7060122a5b91fe8561 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.button','data' => ['class' => ' bg-red-500 hover:bg-red-600','wire:click' => '$toggle(\'revealCreateUntrustedHeaderModal\')','target' => 'revealCreateUntrustedHeaderModal','icon' => 'ban']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => ' bg-red-500 hover:bg-red-600','wire:click' => '$toggle(\'revealCreateUntrustedHeaderModal\')','target' => 'revealCreateUntrustedHeaderModal','icon' => 'ban']); ?>
                    <?php echo e(__('Cancel')); ?>

                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561)): ?>
<?php $attributes = $__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561; ?>
<?php unset($__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald0f1fd2689e4bb7060122a5b91fe8561)): ?>
<?php $component = $__componentOriginald0f1fd2689e4bb7060122a5b91fe8561; ?>
<?php unset($__componentOriginald0f1fd2689e4bb7060122a5b91fe8561); ?>
<?php endif; ?>

                <!--[if BLOCK]><![endif]--><?php if(!$this->roadAccessForm->road_access_rate_limiter): ?>
                    <?php if (isset($component)) { $__componentOriginald0f1fd2689e4bb7060122a5b91fe8561 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.button','data' => ['class' => 'ms-3  bg-green-500 hover:bg-green-600','wire:click' => 'createUntrustedHeader','target' => 'createUntrustedHeader','disabled' => $form->hasErrors(),'icon' => 'cloud-arrow-up']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'ms-3  bg-green-500 hover:bg-green-600','wire:click' => 'createUntrustedHeader','target' => 'createUntrustedHeader','disabled' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($form->hasErrors()),'icon' => 'cloud-arrow-up']); ?>
                        <?php echo e(__('Create')); ?>

                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561)): ?>
<?php $attributes = $__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561; ?>
<?php unset($__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald0f1fd2689e4bb7060122a5b91fe8561)): ?>
<?php $component = $__componentOriginald0f1fd2689e4bb7060122a5b91fe8561; ?>
<?php unset($__componentOriginald0f1fd2689e4bb7060122a5b91fe8561); ?>
<?php endif; ?>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                <?php if (isset($component)) { $__componentOriginala665a74688c74e9ee80d4fedd2b98434 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginala665a74688c74e9ee80d4fedd2b98434 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.action-message','data' => ['class' => 'sm:ms-3 mt-2 sm:mt-0','on' => 'untrustedHeaderCreated']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('action-message'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'sm:ms-3 mt-2 sm:mt-0','on' => 'untrustedHeaderCreated']); ?>
                    <?php echo e(__('Untrusted header created successfully.')); ?>

                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginala665a74688c74e9ee80d4fedd2b98434)): ?>
<?php $attributes = $__attributesOriginala665a74688c74e9ee80d4fedd2b98434; ?>
<?php unset($__attributesOriginala665a74688c74e9ee80d4fedd2b98434); ?>
<?php endif; ?>
<?php if (isset($__componentOriginala665a74688c74e9ee80d4fedd2b98434)): ?>
<?php $component = $__componentOriginala665a74688c74e9ee80d4fedd2b98434; ?>
<?php unset($__componentOriginala665a74688c74e9ee80d4fedd2b98434); ?>
<?php endif; ?>

                <?php if (isset($component)) { $__componentOriginala665a74688c74e9ee80d4fedd2b98434 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginala665a74688c74e9ee80d4fedd2b98434 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.action-message','data' => ['class' => 'sm:ms-3 mt-2 sm:mt-0 bg-red-600 text-red-50','on' => 'untrustedHeaderCreationFailed']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('action-message'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'sm:ms-3 mt-2 sm:mt-0 bg-red-600 text-red-50','on' => 'untrustedHeaderCreationFailed']); ?>
                    <?php echo e(__('Error occured while trying to create untrusted header')); ?>

                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginala665a74688c74e9ee80d4fedd2b98434)): ?>
<?php $attributes = $__attributesOriginala665a74688c74e9ee80d4fedd2b98434; ?>
<?php unset($__attributesOriginala665a74688c74e9ee80d4fedd2b98434); ?>
<?php endif; ?>
<?php if (isset($__componentOriginala665a74688c74e9ee80d4fedd2b98434)): ?>
<?php $component = $__componentOriginala665a74688c74e9ee80d4fedd2b98434; ?>
<?php unset($__componentOriginala665a74688c74e9ee80d4fedd2b98434); ?>
<?php endif; ?>
            </div>

         <?php $__env->endSlot(); ?>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal49bd1c1dd878e22e0fb84faabf295a3f)): ?>
<?php $attributes = $__attributesOriginal49bd1c1dd878e22e0fb84faabf295a3f; ?>
<?php unset($__attributesOriginal49bd1c1dd878e22e0fb84faabf295a3f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal49bd1c1dd878e22e0fb84faabf295a3f)): ?>
<?php $component = $__componentOriginal49bd1c1dd878e22e0fb84faabf295a3f; ?>
<?php unset($__componentOriginal49bd1c1dd878e22e0fb84faabf295a3f); ?>
<?php endif; ?>

</div>
<?php /**PATH C:\Users\<USER>\Herd\laraauthstarter\resources\views/livewire/backend/settings/system/headers/create-untrusted-headers-component.blade.php ENDPATH**/ ?>