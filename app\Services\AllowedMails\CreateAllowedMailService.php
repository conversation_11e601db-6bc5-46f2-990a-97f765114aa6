<?php

namespace App\Services\AllowedMails;

use App\Models\AllowedMail;
use App\DTOs\AllowedMailDTO;
use App\Actions\AllowedMails\CreateAllowedMailAction;
use App\Contracts\AllowedMails\CreateAllowedMailServiceInterface;

class CreateAllowedMailService implements CreateAllowedMailServiceInterface
{
    public function execute(AllowedMailDTO $dto): AllowedMail
    {
        return CreateAllowedMailAction::handle($dto->toArray());
    }
}
