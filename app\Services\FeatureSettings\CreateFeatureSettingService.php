<?php

namespace App\Services\FeatureSettings;

use App\Models\FeatureSetting;
use App\DTOs\FeatureSettingDTO;
use App\Actions\FeatureSettings\CreateFeatureSettingAction;
use App\Contracts\FeatureSettings\CreateFeatureSettingServiceInterface;

class CreateFeatureSettingService implements CreateFeatureSettingServiceInterface
{
    public function execute(FeatureSettingDTO $dto): FeatureSetting
    {
        return CreateFeatureSettingAction::handle($dto->toArray());
    }
}
