    @php
        $tablePagination = $this->allowedExternalIps->isNotEmpty() && $this->totalAllowedExternalIps > 5 ? true : false;
        $allowedExternalIpsAvailable = $tablePagination;
        $rowNumber = 0; // Initialize row number
    @endphp
<div>
    <livewire:backend.settings.system.allowed-external-ips.create-allowed-external-ips-component />
    {{-- Search --}}
    <x-search :results="$allowedExternalIpsAvailable" searchProperty="externalIpsSearch"
        searchPlaceholder="Search by allowed external IPs...." formWidth="max-w-full my-3" />

        {{-- Table --}}
    <x-table :columns="[
        [
            'label' => '#',
            'headerClass' => 'border-slate-600',
        ],
        [
            'label' => 'IP Address',
            'columnName' => 'ip_address',
            'headerClass' => 'border-l border-slate-600 w-32',
        ],
        [
            'label' => 'Description',
            'headerClass' => 'border-l border-slate-600 line-clamp-1',
        ],
        [
            'label' => 'Actions',
            'headerClass' => 'border-l border-slate-600 w-20',
        ],
    ]" :data="$this->allowedExternalIps" captionClass="text-slate-200" :sort-direction="$sortDirection" :sort-column="$sortColumn"
        :results="$allowedExternalIpsAvailable" maxScrollHeight="max-h-[400px] hidden lg:block pr-1"
        theadClass="bg-slate-700 border-l border-slate-700" subtheadClass="px-2"
        rowClass="border-b hover:bg-green-700 hover:text-slate-100 hover:border-y-2 hover:border-slate-600 "
        tableClass="border-2 border-slate-700">
        @foreach ($this->allowedExternalIps as $allowedExternalIp)
            <tr wire:key="{{ 'allowedExternalIp-' . $allowedExternalIp->id }}"
                class="border-b bg-slate-800 hover:bg-green-700 border-slate-700">
                <td class="px-2 py-1  text-slate-200">
                    {{ $loop->iteration }}
                </td>
                <td class="border-l border-slate-600 px-2 py-1 text-slate-200 w-32">
                    {{ $allowedExternalIp->ip_address }}
                </td>
                <td class="border-l border-slate-600 px-2 py-1 text-slate-200  line-clamp-1">
                    {{ $allowedExternalIp->description }}
                </td>
                <td class="border-l border-slate-600 px-2 py-1 w-20">
                    <span class="flex justify-end items-center space-x-1">
                        {{-- Action buttons --}}
                        <livewire:backend.settings.system.allowed-external-ips.view-allowed-external-ips-component :$allowedExternalIp
                            :key="'view-allowedExternalIp-' . $allowedExternalIp->id">
                            <livewire:backend.settings.system.allowed-external-ips.update-allowed-external-ips-component :$allowedExternalIp
                                :key="'update-allowedExternalIp-' . $allowedExternalIp->id">
                                <livewire:backend.settings.system.allowed-external-ips.delete-allowed-external-ips-component :$allowedExternalIp
                                    :key="'delete-allowedExternalIp-' . $allowedExternalIp->id">
                    </span>
                </td>
            </tr>
        @endforeach
    </x-table>

    {{-- Table List --}}
    <x-table-list :data="$this->allowedExternalIps" captionMobileClass="text-slate-200"
        maxScrollHeight="max-h-[500px] lg:hidden pr-1">
        @foreach ($this->allowedExternalIps as $allowedExternalIp)
            @php
                $rowNumber++;
            @endphp
            <div wire:key="{{ 'allowedExternalIp-2-' . $allowedExternalIp->id }}"
                class="p-2 duration-300 ease-in-out rounded-md shadow bg-green-600 text-slate-50 hover:bg-slate-600 hover:shadow-lg">
                <div class="flex justify-between items-center mb-1 pr-1">
                    <span class="text-sm text-slate-100">IP Address:</span>
                    <span class="text-sm text-slate-100">{{ $allowedExternalIp->ip_address }}</span>
                </div>
                <div class="mb-1 block bg-slate-800 rounded px-1.5 py-1">
                    <div class=" text-amber-200">Description:</div>
                    <div class="text-sm text-slate-200 line-clamp-2">{{ $allowedExternalIp->description }}</div>
                </div>
                <div class="flex justify-between items-center">
                    <span class="flex justify-end items-center space-x-1">
                        {{-- Action buttons --}}
                        <livewire:backend.settings.system.allowed-external-ips.view-allowed-external-ips-component :$allowedExternalIp
                            :key="'view-2-allowedExternalIp-' . $allowedExternalIp->id">
                            <livewire:backend.settings.system.allowed-external-ips.update-allowed-external-ips-component :$allowedExternalIp
                                :key="'update-2-allowedExternalIp-' . $allowedExternalIp->id">
                                <livewire:backend.settings.system.allowed-external-ips.delete-allowed-external-ips-component :$allowedExternalIp
                                    :key="'delete-2-allowedExternalIp-' . $allowedExternalIp->id">
                    </span>

                    @if ($rowNumber >= 1)
                        <span
                            class="text-right rounded-full px-2 bg-slate-950 text-slate-50 text-xs">{{ $rowNumber }}</span>
                    @endif
                </div>
            </div>
        @endforeach
    </x-table-list>

    {{-- Pagination --}}
    <x-paginator :pages="$this->allowedExternalIpsPages" :totalRecords="$this->totalAllowedExternalIps" selectClass="text-slate-500"
        paginateRecords="externalIpsPaginate" :results="$allowedExternalIpsAvailable" :paginationEnabled="$tablePagination" />
</div>
