<?php
    $tablePagination = $this->allowedMails->isNotEmpty() && $this->totalAllowedMails > 5 ? true : false;
    $allowedMailsAvailable = $tablePagination;
    $rowNumber = 0; // Initialize row number
?>
<div>
    <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('backend.settings.system.allowed-mails.create-allowed-mails-component', []);

$__html = app('livewire')->mount($__name, $__params, 'lw-1408925444-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>

    
    <div>
        <?php if (isset($component)) { $__componentOriginal2848fab3424fc8162748b5c6984d5047 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2848fab3424fc8162748b5c6984d5047 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.filter','data' => ['filters' => [
            [
                'name' => 'filterMailsByStatus',
                'label' => 'Choose by status',
                'options' => $this->mailStatusOptions,
            ],
        ],'results' => $allowedMailsAvailable]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filter'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['filters' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
            [
                'name' => 'filterMailsByStatus',
                'label' => 'Choose by status',
                'options' => $this->mailStatusOptions,
            ],
        ]),'results' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($allowedMailsAvailable)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2848fab3424fc8162748b5c6984d5047)): ?>
<?php $attributes = $__attributesOriginal2848fab3424fc8162748b5c6984d5047; ?>
<?php unset($__attributesOriginal2848fab3424fc8162748b5c6984d5047); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2848fab3424fc8162748b5c6984d5047)): ?>
<?php $component = $__componentOriginal2848fab3424fc8162748b5c6984d5047; ?>
<?php unset($__componentOriginal2848fab3424fc8162748b5c6984d5047); ?>
<?php endif; ?>
    </div>

    
    <?php if (isset($component)) { $__componentOriginal9b33c063a2222f59546ad2a2a9a94bc6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9b33c063a2222f59546ad2a2a9a94bc6 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.search','data' => ['results' => $allowedMailsAvailable,'searchProperty' => 'allowedMailsSearch','searchPlaceholder' => 'Search by mails and descriptions....','formWidth' => 'max-w-full my-3']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('search'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['results' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($allowedMailsAvailable),'searchProperty' => 'allowedMailsSearch','searchPlaceholder' => 'Search by mails and descriptions....','formWidth' => 'max-w-full my-3']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9b33c063a2222f59546ad2a2a9a94bc6)): ?>
<?php $attributes = $__attributesOriginal9b33c063a2222f59546ad2a2a9a94bc6; ?>
<?php unset($__attributesOriginal9b33c063a2222f59546ad2a2a9a94bc6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9b33c063a2222f59546ad2a2a9a94bc6)): ?>
<?php $component = $__componentOriginal9b33c063a2222f59546ad2a2a9a94bc6; ?>
<?php unset($__componentOriginal9b33c063a2222f59546ad2a2a9a94bc6); ?>
<?php endif; ?>

    
    <?php if (isset($component)) { $__componentOriginal163c8ba6efb795223894d5ffef5034f5 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal163c8ba6efb795223894d5ffef5034f5 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.table','data' => ['columns' => [
        ['label' => '#', 'headerClass' => 'border-slate-600'],
        [
            'label' => 'Email',
            'columnName' => 'email',
            'headerClass' => 'border-l border-slate-600 w-32'
        ],
        [
            'label' => 'Status',
            'columnName' => 'status',
            'headerClass' => 'border-l border-slate-600 w-32'
        ],
        ['label' => 'Description', 'headerClass' => 'border-l border-slate-600 line-clamp-1'],
        ['label' => 'Actions', 'headerClass' => 'border-l border-slate-600 w-20'],
    ],'data' => $this->allowedMails,'captionClass' => 'text-slate-200','sortDirection' => $sortDirection,'sortColumn' => $sortColumn,'results' => $allowedMailsAvailable,'maxScrollHeight' => 'max-h-[400px] hidden lg:block pr-1','theadClass' => 'bg-slate-700 border-l border-slate-700','subtheadClass' => 'px-2','rowClass' => 'border-b hover:bg-blue-700 hover:text-slate-100 hover:border-y-2 hover:border-slate-600 ','tableClass' => 'border-2 border-slate-700']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('table'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['columns' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute([
        ['label' => '#', 'headerClass' => 'border-slate-600'],
        [
            'label' => 'Email',
            'columnName' => 'email',
            'headerClass' => 'border-l border-slate-600 w-32'
        ],
        [
            'label' => 'Status',
            'columnName' => 'status',
            'headerClass' => 'border-l border-slate-600 w-32'
        ],
        ['label' => 'Description', 'headerClass' => 'border-l border-slate-600 line-clamp-1'],
        ['label' => 'Actions', 'headerClass' => 'border-l border-slate-600 w-20'],
    ]),'data' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($this->allowedMails),'captionClass' => 'text-slate-200','sort-direction' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($sortDirection),'sort-column' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($sortColumn),'results' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($allowedMailsAvailable),'maxScrollHeight' => 'max-h-[400px] hidden lg:block pr-1','theadClass' => 'bg-slate-700 border-l border-slate-700','subtheadClass' => 'px-2','rowClass' => 'border-b hover:bg-blue-700 hover:text-slate-100 hover:border-y-2 hover:border-slate-600 ','tableClass' => 'border-2 border-slate-700']); ?>
        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $this->allowedMails; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $allowedMail): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <tr wire:key="<?php echo e('allowedMail-' . $allowedMail->id); ?>"
                class="border-b bg-slate-800 hover:bg-blue-700 border-slate-700">
                <td class="px-2 py-1  text-slate-200">
                    <?php echo e($loop->iteration); ?>

                </td>
                <td class="border-l border-slate-600 px-2 py-1 text-slate-200 w-32">
                    <?php echo e($allowedMail->email); ?>

                </td>
                <td class="border-l border-slate-600 px-2 py-1 text-slate-200 w-32">
                    <?php echo e($allowedMail->status->label()); ?>

                </td>
                <td class="border-l border-slate-600 px-2 py-1 text-slate-200  line-clamp-1">
                    <?php echo e($allowedMail->description); ?>

                </td>
                <td class="border-l border-slate-600 px-2 py-1 w-20">
                    <span class="flex justify-end items-center space-x-1">
                        
                        <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('backend.settings.system.allowed-mails.view-allowed-mails-component', ['allowedMail' => $allowedMail]);

$__html = app('livewire')->mount($__name, $__params, 'view-allowedMail-' . $allowedMail->id, $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                            <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('backend.settings.system.allowed-mails.update-allowed-mails-component', ['allowedMail' => $allowedMail]);

$__html = app('livewire')->mount($__name, $__params, 'update-allowedMail-' . $allowedMail->id, $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                                <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('backend.settings.system.allowed-mails.delete-allowed-mails-component', ['allowedMail' => $allowedMail]);

$__html = app('livewire')->mount($__name, $__params, 'delete-allowedMail-' . $allowedMail->id, $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                    </span>
                </td>
            </tr>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal163c8ba6efb795223894d5ffef5034f5)): ?>
<?php $attributes = $__attributesOriginal163c8ba6efb795223894d5ffef5034f5; ?>
<?php unset($__attributesOriginal163c8ba6efb795223894d5ffef5034f5); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal163c8ba6efb795223894d5ffef5034f5)): ?>
<?php $component = $__componentOriginal163c8ba6efb795223894d5ffef5034f5; ?>
<?php unset($__componentOriginal163c8ba6efb795223894d5ffef5034f5); ?>
<?php endif; ?>

    
    <?php if (isset($component)) { $__componentOriginalace4d1b2e22e971ae2a2b53aefed4e19 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalace4d1b2e22e971ae2a2b53aefed4e19 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.table-list','data' => ['data' => $this->allowedMails,'captionMobileClass' => 'text-slate-200','maxScrollHeight' => 'max-h-[500px] lg:hidden pr-1']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('table-list'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['data' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($this->allowedMails),'captionMobileClass' => 'text-slate-200','maxScrollHeight' => 'max-h-[500px] lg:hidden pr-1']); ?>
        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $this->allowedMails; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $allowedMail): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php
                $rowNumber++;
            ?>
            <div wire:key="<?php echo e('allowedMail-2-' . $allowedMail->id); ?>"
                class="p-2 duration-300 ease-in-out rounded-md shadow bg-blue-600 text-slate-50 hover:bg-slate-600 hover:shadow-lg">
                <div class="flex justify-between items-center mb-1 pr-1">
                    <span class="text-sm text-slate-100">Email:</span>
                    <span class="text-sm text-slate-100 line-clamp-1"><?php echo e($allowedMail->email); ?></span>
                </div>
                <div class="flex justify-between items-center mb-1 pr-1">
                    <span class="text-sm text-slate-100">Status:</span>
                    <span class="text-sm text-slate-100"><?php echo e($allowedMail->status->label()); ?></span>
                </div>
                <div class="mb-1 block bg-slate-800 rounded px-1.5 py-1">
                    <div class=" text-amber-200">Description:</div>
                    <div class="text-sm text-slate-200 line-clamp-2"><?php echo e($allowedMail->description); ?></div>
                </div>
                <div class="flex justify-between items-center">
                    <span class="flex justify-end items-center space-x-1">
                        
                        <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('backend.settings.system.allowed-mails.view-allowed-mails-component', ['allowedMail' => $allowedMail]);

$__html = app('livewire')->mount($__name, $__params, 'view-2-allowedMail-' . $allowedMail->id, $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                            <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('backend.settings.system.allowed-mails.update-allowed-mails-component', ['allowedMail' => $allowedMail]);

$__html = app('livewire')->mount($__name, $__params, 'update-2-allowedMail-' . $allowedMail->id, $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                                <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('backend.settings.system.allowed-mails.delete-allowed-mails-component', ['allowedMail' => $allowedMail]);

$__html = app('livewire')->mount($__name, $__params, 'delete-2-allowedMail-' . $allowedMail->id, $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                    </span>
                    <!--[if BLOCK]><![endif]--><?php if($rowNumber >= 1): ?>
                        <span
                            class="text-right rounded-full px-2 bg-slate-950 text-slate-50 text-xs"><?php echo e($rowNumber); ?></span>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </div>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalace4d1b2e22e971ae2a2b53aefed4e19)): ?>
<?php $attributes = $__attributesOriginalace4d1b2e22e971ae2a2b53aefed4e19; ?>
<?php unset($__attributesOriginalace4d1b2e22e971ae2a2b53aefed4e19); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalace4d1b2e22e971ae2a2b53aefed4e19)): ?>
<?php $component = $__componentOriginalace4d1b2e22e971ae2a2b53aefed4e19; ?>
<?php unset($__componentOriginalace4d1b2e22e971ae2a2b53aefed4e19); ?>
<?php endif; ?>

    
    <?php if (isset($component)) { $__componentOriginal9a81ef5e6386ad2695ad8705a47608cf = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9a81ef5e6386ad2695ad8705a47608cf = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.paginator','data' => ['pages' => $this->allowedMailsPages,'totalRecords' => $this->totalAllowedMails,'selectClass' => 'text-slate-500','paginateRecords' => 'allowedMailsPaginate','results' => $allowedMailsAvailable,'paginationEnabled' => $tablePagination]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('paginator'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['pages' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($this->allowedMailsPages),'totalRecords' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($this->totalAllowedMails),'selectClass' => 'text-slate-500','paginateRecords' => 'allowedMailsPaginate','results' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($allowedMailsAvailable),'paginationEnabled' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($tablePagination)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9a81ef5e6386ad2695ad8705a47608cf)): ?>
<?php $attributes = $__attributesOriginal9a81ef5e6386ad2695ad8705a47608cf; ?>
<?php unset($__attributesOriginal9a81ef5e6386ad2695ad8705a47608cf); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9a81ef5e6386ad2695ad8705a47608cf)): ?>
<?php $component = $__componentOriginal9a81ef5e6386ad2695ad8705a47608cf; ?>
<?php unset($__componentOriginal9a81ef5e6386ad2695ad8705a47608cf); ?>
<?php endif; ?>
</div>
<?php /**PATH C:\Users\<USER>\Herd\laraauthstarter\resources\views/livewire/backend/settings/system/allowed-mails/show-allowed-mails-component.blade.php ENDPATH**/ ?>