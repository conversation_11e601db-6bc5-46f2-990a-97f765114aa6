<?php

namespace App\Repositories\FeatureSettings;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use App\Contracts\Repos\FeatureSettingsRepos\GetFeatureSettingsRepositoryInterface;

final class CachedGetFeatureSettingsRepository implements GetFeatureSettingsRepositoryInterface
{
    public function __construct(private GetFeatureSettingsRepositoryInterface $getFeatureSettingsRepository) {}

    public function handle(int $limit = 10, array $selectedColumns = ['*']): Collection
    {
        $fresh = 300; // 5 minutes
        $stale = 900; // 15 minutes

        $columns = $selectedColumns;
        sort($columns);
        $key = 'feature_settings_list:' . $limit . ':' . implode(',', $columns);

        return Cache::flexible($key, [$fresh, $stale], function () use ($limit, $selectedColumns) {
            return $this->getFeatureSettingsRepository->handle($limit, $selectedColumns);
        });
    }
}
