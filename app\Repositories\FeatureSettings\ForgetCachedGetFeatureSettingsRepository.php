<?php

namespace App\Repositories\FeatureSettings;

use Illuminate\Support\Facades\Cache;
use App\Contracts\Repos\FeatureSettingsRepos\ForgetCachedGetFeatureSettingsRepositoryInterface;

final class ForgetCachedGetFeatureSettingsRepository implements ForgetCachedGetFeatureSettingsRepositoryInterface
{
    public function handle(int $limit = 10, array $selectedColumns = ['*']): void
    {
        $columns = $selectedColumns;
        sort($columns);
        $key = 'feature_settings_list:' . $limit . ':' . implode(',', $columns);
        Cache::forget($key);
    }
}
