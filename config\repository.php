<?php

return [
    // Global caching toggle (if true, all repos cache)
    'cache_enabled' => env('REPOSITORY_CACHE', false),

    // Per-model toggles, used only if global toggle is false
    'models' => [
        'roles'       => env('REPOSITORY_CACHE_ROLES', false),
        'positions'       => env('REPOSITORY_CACHE_POSITIONS', false),
        'permissions' => env('REPOSITORY_CACHE_PERMISSIONS', false),
        'users'      => env('REPOSITORY_CACHE_USERS', false),
        'settings'   => env('REPOSITORY_CACHE_SETTINGS', false),
        'teams'      => env('REPOSITORY_CACHE_TEAMS', false),
        'users_teams' => env('REPOSITORY_CACHE_USERS_TEAMS', false),
        'users_roles' => env('REPOSITORY_CACHE_USERS_ROLES', false),
        'feature_settings' => env('REP<PERSON><PERSON>OR<PERSON>_CACHE_FEATURE_SETTINGS', false),
    ],
];
