<?php

namespace App\Livewire\Forms\AllowedMails;

use Livewire\Form;
use App\Models\AllowedMail;
use App\Rules\GibberishRule;
use Livewire\Attributes\Validate;
use Illuminate\Validation\Rule;
use App\Enums\AllowedMails\AllowedMailStatusEnum;

class AllowedMailsForm extends Form
{
    #[Validate]
    public $email;
    #[Validate]
    public $status;
    #[Validate]
    public $description;

    public ?AllowedMail $selectedAllowedMail = null;

    public function rules(): array
    {
        return [
            'email' => array_filter([
                'required',
                'email',
                'string',
                'min:5',
                'max:255',
                new GibberishRule('email', 3, 2),
                Rule::unique('allowed_mails')->ignore($this->selectedAllowedMail)
            ]),
            'status' => [
                'required',
                Rule::in(array_column(AllowedMailStatusEnum::cases(), 'value'))
            ],
            'description' => array_filter([
                'required',
                'string',
                'min:5',
                'max:500',
                new GibberishRule('description', 3, 2)
            ]),
        ];
    }

    public function resetForm()
    {
        $this->reset(['email', 'status', 'description']);
    }

    public function hasErrors(): bool
    {
        return $this->getErrorBag()->isNotEmpty() || empty($this->email) || empty($this->status) || empty($this->description);
    }

    public function getCreateAllowedMailEvent(): string
    {
        return $this->hasErrors() ? '' : 'createAllowedMail';
    }

    public function getUpdateAllowedMailEvent(): string
    {
        return $this->hasErrors() ? '' : 'updateAllowedMail';
    }
}
