<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'columns' => [],
    'data' => null,
    'results' => false,
    'sortDirection' => 'DESC',
    'sortColumn' => 'id',
    'captionClass' => 'hidden lg:block',
    'maxScrollHeight' => 'max-h-[350px] hidden lg:block',
    'tableContainerClass' => '',
    'tableClass' => '',
    'theadClass' => '',
    'subtheadClass' => '',
    'rowClass' => '',
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'columns' => [],
    'data' => null,
    'results' => false,
    'sortDirection' => 'DESC',
    'sortColumn' => 'id',
    'captionClass' => 'hidden lg:block',
    'maxScrollHeight' => 'max-h-[350px] hidden lg:block',
    'tableContainerClass' => '',
    'tableClass' => '',
    'theadClass' => '',
    'subtheadClass' => '',
    'rowClass' => '',
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars, $__key, $__value); ?>

<h1 class="mb-2  text-lg font-bold text-left rtl:text-right  <?php echo e($captionClass); ?>">
    <?php echo e($caption ?? ''); ?>

</h1>
<div class="overflow-y-auto custom-scrollbar shadow-sm <?php echo e($maxScrollHeight); ?>">
    <div class="relative overflow-x-auto shadow-md rounded-lg <?php echo e($tableContainerClass); ?>">
        <table class="w-full text-sm text-left rtl:text-right text-slate-400 <?php echo e($tableClass); ?>">
            <thead class="text-xs uppercase <?php echo e($theadClass); ?>">
                <tr>
                    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $columns; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $column): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php
                            $colName = $column['columnName'] ?? null;
                        ?>

                        <th <?php if($results && $colName): ?> wire:click="doSort('<?php echo e($colName); ?>')" role="button" <?php endif; ?>
                            scope="col"
                            class="cursor-pointer py-2 <?php echo e($subtheadClass ?? ''); ?> <?php echo e($column['headerClass'] ?? ''); ?>">
                            <span class="flex items-center space-x-1.5">
                                <span><?php echo e($column['label']); ?></span>
                                <!--[if BLOCK]><![endif]--><?php if($results && $colName): ?>
                                    <?php if (isset($component)) { $__componentOriginalf2072dd6c598681ce7b631df4436c6ad = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf2072dd6c598681ce7b631df4436c6ad = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.sort-columns','data' => ['columnName' => $colName,'sortDirection' => $sortDirection,'sortColumn' => $sortColumn]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('sort-columns'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['columnName' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($colName),'sort-direction' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($sortDirection),'sort-column' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($sortColumn)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf2072dd6c598681ce7b631df4436c6ad)): ?>
<?php $attributes = $__attributesOriginalf2072dd6c598681ce7b631df4436c6ad; ?>
<?php unset($__attributesOriginalf2072dd6c598681ce7b631df4436c6ad); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf2072dd6c598681ce7b631df4436c6ad)): ?>
<?php $component = $__componentOriginalf2072dd6c598681ce7b631df4436c6ad; ?>
<?php unset($__componentOriginalf2072dd6c598681ce7b631df4436c6ad); ?>
<?php endif; ?>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            </span>

                        </th>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                </tr>
            </thead>

            <tbody>
                <!--[if BLOCK]><![endif]--><?php if($data->isNotEmpty()): ?>
                    <?php echo e($slot); ?>

                <?php else: ?>
                    <tr class=" bg-slate-800  border-slate-700  <?php echo e($rowClass); ?>">
                        <td colspan="<?php echo e(count($columns)); ?>" class="px-6 py-4 text-center text-slate-400 ">
                            <?php if (isset($component)) { $__componentOriginala37bee3be1b7b9366ae2ff14e78e37db = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginala37bee3be1b7b9366ae2ff14e78e37db = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.notice','data' => ['class' => 'grid grid-cols-1 bg-amber-100 text-amber-700 py-1 border mt-1','noticeClass' => 'uppercase text-xs']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('notice'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'grid grid-cols-1 bg-amber-100 text-amber-700 py-1 border mt-1','noticeClass' => 'uppercase text-xs']); ?>
                                 <?php $__env->slot('notice', null, []); ?> <?php echo e('Oops!'); ?> <?php $__env->endSlot(); ?>
                                <?php echo e(__('No records found in the database')); ?>

                             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginala37bee3be1b7b9366ae2ff14e78e37db)): ?>
<?php $attributes = $__attributesOriginala37bee3be1b7b9366ae2ff14e78e37db; ?>
<?php unset($__attributesOriginala37bee3be1b7b9366ae2ff14e78e37db); ?>
<?php endif; ?>
<?php if (isset($__componentOriginala37bee3be1b7b9366ae2ff14e78e37db)): ?>
<?php $component = $__componentOriginala37bee3be1b7b9366ae2ff14e78e37db; ?>
<?php unset($__componentOriginala37bee3be1b7b9366ae2ff14e78e37db); ?>
<?php endif; ?>
                        </td>
                    </tr>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            </tbody>
        </table>
    </div>
</div>
<?php /**PATH C:\Users\<USER>\Herd\laraauthstarter\resources\views/components/table.blade.php ENDPATH**/ ?>