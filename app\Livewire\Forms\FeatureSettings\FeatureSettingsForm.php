<?php

namespace App\Livewire\Forms\FeatureSettings;

use Livewire\Form;
use App\Models\FeatureSetting;
use App\Rules\GibberishRule;
use Illuminate\Validation\Rule;
use Livewire\Attributes\Validate;
use App\Enums\FeatureSettings\FeatureSettingValueEnum;

class FeatureSettingsForm extends Form
{
    #[Validate]
    public $feature;
    #[Validate]
    public $value;

    public ?FeatureSetting $selectedFeatureSetting = null;

    public function rules(): array
    {
        return [
            'feature' => array_filter([
                'required',
                'string',
                'min:3',
                'max:100',
                new GibberishRule('feature', 3, 2),
                Rule::unique('feature_settings')->ignore($this->selectedFeatureSetting)
            ]),
            'value' => [
                'required',
                Rule::in(array_column(FeatureSettingValueEnum::cases(), 'value'))
            ],
        ];
    }

    public function resetForm()
    {
        $this->reset(['feature', 'value']);
    }

    public function hasErrors(): bool
    {
        return $this->getErrorBag()->isNotEmpty() || empty($this->feature) || empty($this->value);
    }

    public function getCreateFeatureSettingEvent(): string
    {
        return $this->hasErrors() ? '' : 'createFeatureSetting';
    }

    public function getUpdateFeatureSettingEvent(): string
    {
        return $this->hasErrors() ? '' : 'updateFeatureSetting';
    }
}
