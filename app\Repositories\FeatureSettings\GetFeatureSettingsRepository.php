<?php

namespace App\Repositories\FeatureSettings;

use App\Models\FeatureSetting;
use Illuminate\Support\Collection;
use App\Contracts\Repos\FeatureSettingsRepos\GetFeatureSettingsRepositoryInterface;

final class GetFeatureSettingsRepository implements GetFeatureSettingsRepositoryInterface
{
    public function handle(int $limit = 10, array $selectedColumns = ['*']): Collection
    {
        return FeatureSetting::select($selectedColumns)
            ->orderByIdDesc()
            ->limit($limit)
            ->get();
    }
}
