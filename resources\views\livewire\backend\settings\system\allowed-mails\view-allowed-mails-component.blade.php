<div>
    <x-action-buttons :id="$allowedMail->id" viewAction="openModalToViewAllowedMail" />

    <x-dialog-modal wire:model="revealViewAllowedMailModal" :maxWidth="'xl'">
        <x-slot name="content">
            <x-divider nameClass="text-blue-400">
                {{ __('READ ABOUT ALLOWED MAIL') }}
            </x-divider>

            <div class="text-slate-200 text-base font-semibold my-2">
                Email address: {{ $allowedMail->email }}
            </div>
            <x-notice class="bg-blue-600 text-amber-50 py-2 border mt-1 " noticeClass="uppercase text-xs font-bold">
                <x-slot:notice>Description:</x-slot>
                <div class="text-sm text-slate-100 my-2">
                    {{ $allowedMail->description }}
                </div>
            </x-notice>
            <div class="text-slate-200 text-sm font-semibold my-2">
                Status: {{ $allowedMail->status->label() }}
            </div>

            <div class="sm:flex items-center mt-4">
                <x-button class=" bg-blue-600 hover:bg-blue-700" wire:click="$toggle('revealViewAllowedMailModal')"
                    target="revealViewAllowedMailModal" icon="thumbs-up">
                    {{ __('Done') }}
                </x-button>
            </div>
        </x-slot>
    </x-dialog-modal>
</div>
