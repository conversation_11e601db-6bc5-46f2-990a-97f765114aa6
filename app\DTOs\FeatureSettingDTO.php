<?php

namespace App\DTOs;

use App\Enums\FeatureSettings\FeatureSettingValueEnum;

class FeatureSettingDTO
{
    public function __construct(
        public readonly ?string $feature = null,
        public readonly int $value = FeatureSettingValueEnum::ENABLED->value,

    ) {}

    public function toArray(): array
    {
        return array_filter([
            'feature' => $this->feature,
            'value' => $this->value,
        ], fn($value) => !is_null($value));
    }
}
