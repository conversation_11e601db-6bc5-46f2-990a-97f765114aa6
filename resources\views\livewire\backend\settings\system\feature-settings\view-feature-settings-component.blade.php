<div>
    <x-action-buttons :id="$featureSetting->id" viewAction="openModalToViewFeatureSetting" />

    <x-dialog-modal wire:model="revealViewFeatureSettingModal" :maxWidth="'xl'">
        <x-slot name="content">
            <x-divider nameClass="text-green-400">
                {{ __('VIEW FEATURE SETTING') }}
            </x-divider>

            <div class="space-y-4 mt-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-slate-300">Feature Name</label>
                        <div class="mt-1 p-2 bg-slate-700 rounded-md text-slate-100">
                            {{ $featureSetting->feature }}
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-slate-300">Value</label>
                        <div class="mt-1 p-2 bg-slate-700 rounded-md">
                            <x-boolean-badge value="{{ $featureSetting->value->value === 2 ? true : false }}" trueStatement="Enabled"
                                falseStatement="Disabled" />
                        </div>
                    </div>

                </div>
            </div>

            <div class="flex justify-end mt-6">
                <x-button class="bg-red-600 hover:bg-red-700" wire:click="$toggle('revealViewFeatureSettingModal')"
                    target="revealViewFeatureSettingModal" icon="times">
                    {{ __('Close') }}
                </x-button>
            </div>
        </x-slot>
    </x-dialog-modal>
</div>
