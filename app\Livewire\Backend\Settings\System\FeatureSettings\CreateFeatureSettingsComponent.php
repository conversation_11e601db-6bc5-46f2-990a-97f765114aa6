<?php

namespace App\Livewire\Backend\Settings\System\FeatureSettings;

use Livewire\Component;
use App\DTOs\FeatureSettingDTO;
use Livewire\Attributes\Computed;
use App\Facades\Securities\HoneypotFacade;
use App\Enums\FeatureSettings\FeatureSettingValueEnum;
use App\Livewire\Forms\Securities\RoadAccessForm;
use App\Livewire\Forms\FeatureSettings\FeatureSettingsForm;
use App\Facades\FeatureSettings\CreateFeatureSettingFacade;
use App\Facades\Securities\OriginHeaderValidatorFacade;

class CreateFeatureSettingsComponent extends Component
{
    public FeatureSettingsForm $form;
    public RoadAccessForm $roadAccessForm;

    public $revealCreateFeatureSettingsModal = false;

    public function openModalToCreateFeatureSetting()
    {
        $this->resetErrorBag();
        $this->form->resetForm();
        $this->roadAccessForm->generateTrackingTime();
        if ($this->roadAccessForm->checkUserAttempts('CreateFeatureSetting:' . request()->ip(), 6, 300)) {return;}
        $this->revealCreateFeatureSettingsModal = true;
    }

    public function createFeatureSetting()
    {
        $validated = $this->form->validate();
        if ($this->roadAccessForm->checkUserAttempts('CreateFeatureSetting:' . request()->ip(), 6, 300)) {return;}

        OriginHeaderValidatorFacade::validate();
        HoneypotFacade::inspect($this->roadAccessForm);
        $dto = new FeatureSettingDTO(...$validated);

        try {
            CreateFeatureSettingFacade::execute($dto);
            $this->dispatch('featureSettingCreated');
            $this->form->resetForm();
        } catch (\Throwable $th) {
            $this->dispatch('featureSettingCreationFailed');
        }
    }

    #[Computed]
    public function featureSettingValueOptions()
    {
        return FeatureSettingValueEnum::getFeatureSettingValueOptions();
    }

    public function render()
    {
        return view('livewire.backend.settings.system.feature-settings.create-feature-settings-component');
    }
}
