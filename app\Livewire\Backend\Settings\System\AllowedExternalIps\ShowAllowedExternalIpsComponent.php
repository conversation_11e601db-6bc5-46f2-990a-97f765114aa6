<?php

namespace App\Livewire\Backend\Settings\System\AllowedExternalIps;

use Livewire\Component;
use Livewire\Attributes\On;
use Livewire\Attributes\Url;
use App\Models\AllowedExternalIp;
use Livewire\Attributes\Computed;
use App\Supports\PaginatorSupport;
use App\Traits\Defaults\HasSortables;
use App\Facades\Repos\AllowedExternalIps\CountAllowedExternalIpsFacade;

class ShowAllowedExternalIpsComponent extends Component
{
    use HasSortables;

    #[Url]
    public $externalIpsSearch = '';
    #[Url]
    public $externalIpsPaginate = 5;

    public function doSort(string $column)
    {
        $this->sortingDirection($column);
    }

    #[Computed]
    public function allowedExternalIps()
    {
        $selectedColumns = ['id', 'ip_address', 'description'];
        $searchTerm = trim('%' . $this->externalIpsSearch . '%');

        $query = AllowedExternalIp::query();
        $query = $query->select($selectedColumns);
        $query = $query->search($searchTerm);
        $query = $this->sortBy($query, $this->sortColumn, $this->sortDirection);
        $query = $query->paginate($this->externalIpsPaginate);

        return $query;
    }

    #[Computed]
    public function totalAllowedExternalIps()
    {
        return CountAllowedExternalIpsFacade::handle();
    }

    #[Computed]
    public function allowedExternalIpsPages(): array
    {
        return PaginatorSupport::generatePages($this->totalAllowedExternalIps);
    }

    #[On(['allowedExternalIpCreated', 'allowedExternalIpUpdated', 'allowedExternalIpDeleted'])]
    public function render()
    {
        return view('livewire.backend.settings.system.allowed-external-ips.show-allowed-external-ips-component');
    }

    public function placeholder()
    {
        return view('placeholders.lazy-spin-loader');
    }
}
