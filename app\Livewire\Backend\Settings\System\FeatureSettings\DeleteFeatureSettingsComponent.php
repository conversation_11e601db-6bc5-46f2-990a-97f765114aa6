<?php

namespace App\Livewire\Backend\Settings\System\FeatureSettings;

use Livewire\Component;
use App\Models\FeatureSetting;
use Livewire\Attributes\Locked;
use App\Livewire\Forms\Securities\RoadAccessForm;
use App\Facades\FeatureSettings\DeleteFeatureSettingFacade;

class DeleteFeatureSettingsComponent extends Component
{
    #[Locked]
    public FeatureSetting $featureSetting;
    public RoadAccessForm $roadAccessForm;

    public $revealDeleteFeatureSettingModal = false;

    public function openModalToDeleteFeatureSetting()
    {
        $this->roadAccessForm->generateTrackingTime();
        $this->revealDeleteFeatureSettingModal = true;
    }

    public function deleteFeatureSetting()
    {
        if ($this->roadAccessForm->checkUserAttempts('DeleteFeatureSetting:' . request()->ip(), 6, 300)) {return;}
        try {
            DeleteFeatureSettingFacade::execute($this->featureSetting);
            $this->revealDeleteFeatureSettingModal = false;
            $this->dispatch('featureSettingDeleted');
            $this->showSuccessSweetAlert('Deleted', 'Feature setting deleted successfully');
        } catch (\Throwable $th) {
            $this->dispatch('featureSettingDeletionFailed');
        }
    }

    public function render()
    {
        return view('livewire.backend.settings.system.feature-settings.delete-feature-settings-component');
    }
}
